package com.example.meals.dto;

import java.util.List;

/**
 * 营养分析响应DTO
 */
public class NutritionAnalysisResponse {
    private FoodResponse food;
    private Double weight;
    private CalculatedNutrition calculatedNutrition;
    private NutritionRecommendation recommendation;
    
    // 计算后的营养成分
    public static class CalculatedNutrition {
        private Double calories;
        private Double protein;
        private Double fat;
        private Double carbohydrates;
        private Double fiber;
        private Double sugar;
        private Double sodium;
        private Double calcium;
        private Double iron;
        private Double vitaminC;
        private Double vitaminA;
        private Double vitaminD;
        private Double vitaminE;
        private Double vitaminB1;
        private Double vitaminB2;
        private Double vitaminB6;
        private Double vitaminB12;
        private Double folicAcid;
        private Double potassium;
        private Double magnesium;
        private Double zinc;
        private Double phosphorus;
        
        // Getter 和 Setter 方法
        public Double getCalories() { return calories; }
        public void setCalories(Double calories) { this.calories = calories; }
        
        public Double getProtein() { return protein; }
        public void setProtein(Double protein) { this.protein = protein; }
        
        public Double getFat() { return fat; }
        public void setFat(Double fat) { this.fat = fat; }
        
        public Double getCarbohydrates() { return carbohydrates; }
        public void setCarbohydrates(Double carbohydrates) { this.carbohydrates = carbohydrates; }
        
        public Double getFiber() { return fiber; }
        public void setFiber(Double fiber) { this.fiber = fiber; }
        
        public Double getSugar() { return sugar; }
        public void setSugar(Double sugar) { this.sugar = sugar; }
        
        public Double getSodium() { return sodium; }
        public void setSodium(Double sodium) { this.sodium = sodium; }
        
        public Double getCalcium() { return calcium; }
        public void setCalcium(Double calcium) { this.calcium = calcium; }
        
        public Double getIron() { return iron; }
        public void setIron(Double iron) { this.iron = iron; }
        
        public Double getVitaminC() { return vitaminC; }
        public void setVitaminC(Double vitaminC) { this.vitaminC = vitaminC; }
        
        public Double getVitaminA() { return vitaminA; }
        public void setVitaminA(Double vitaminA) { this.vitaminA = vitaminA; }
        
        public Double getVitaminD() { return vitaminD; }
        public void setVitaminD(Double vitaminD) { this.vitaminD = vitaminD; }
        
        public Double getVitaminE() { return vitaminE; }
        public void setVitaminE(Double vitaminE) { this.vitaminE = vitaminE; }
        
        public Double getVitaminB1() { return vitaminB1; }
        public void setVitaminB1(Double vitaminB1) { this.vitaminB1 = vitaminB1; }
        
        public Double getVitaminB2() { return vitaminB2; }
        public void setVitaminB2(Double vitaminB2) { this.vitaminB2 = vitaminB2; }
        
        public Double getVitaminB6() { return vitaminB6; }
        public void setVitaminB6(Double vitaminB6) { this.vitaminB6 = vitaminB6; }
        
        public Double getVitaminB12() { return vitaminB12; }
        public void setVitaminB12(Double vitaminB12) { this.vitaminB12 = vitaminB12; }
        
        public Double getFolicAcid() { return folicAcid; }
        public void setFolicAcid(Double folicAcid) { this.folicAcid = folicAcid; }
        
        public Double getPotassium() { return potassium; }
        public void setPotassium(Double potassium) { this.potassium = potassium; }
        
        public Double getMagnesium() { return magnesium; }
        public void setMagnesium(Double magnesium) { this.magnesium = magnesium; }
        
        public Double getZinc() { return zinc; }
        public void setZinc(Double zinc) { this.zinc = zinc; }
        
        public Double getPhosphorus() { return phosphorus; }
        public void setPhosphorus(Double phosphorus) { this.phosphorus = phosphorus; }
    }
    
    // 营养建议
    public static class NutritionRecommendation {
        private String caloriesLevel;      // 热量水平：低/中/高
        private String proteinLevel;      // 蛋白质水平：低/中/高
        private String fatLevel;          // 脂肪水平：低/中/高
        private String carbsLevel;        // 碳水化合物水平：低/中/高
        private String fiberLevel;        // 膳食纤维水平：低/中/高
        private String sodiumLevel;       // 钠含量水平：低/中/高
        private List<String> healthTips;  // 健康建议
        
        // Getter 和 Setter 方法
        public String getCaloriesLevel() { return caloriesLevel; }
        public void setCaloriesLevel(String caloriesLevel) { this.caloriesLevel = caloriesLevel; }
        
        public String getProteinLevel() { return proteinLevel; }
        public void setProteinLevel(String proteinLevel) { this.proteinLevel = proteinLevel; }
        
        public String getFatLevel() { return fatLevel; }
        public void setFatLevel(String fatLevel) { this.fatLevel = fatLevel; }
        
        public String getCarbsLevel() { return carbsLevel; }
        public void setCarbsLevel(String carbsLevel) { this.carbsLevel = carbsLevel; }
        
        public String getFiberLevel() { return fiberLevel; }
        public void setFiberLevel(String fiberLevel) { this.fiberLevel = fiberLevel; }
        
        public String getSodiumLevel() { return sodiumLevel; }
        public void setSodiumLevel(String sodiumLevel) { this.sodiumLevel = sodiumLevel; }
        
        public List<String> getHealthTips() { return healthTips; }
        public void setHealthTips(List<String> healthTips) { this.healthTips = healthTips; }
    }
    
    // Getter 和 Setter 方法
    public FoodResponse getFood() { return food; }
    public void setFood(FoodResponse food) { this.food = food; }
    
    public Double getWeight() { return weight; }
    public void setWeight(Double weight) { this.weight = weight; }
    
    public CalculatedNutrition getCalculatedNutrition() { return calculatedNutrition; }
    public void setCalculatedNutrition(CalculatedNutrition calculatedNutrition) { this.calculatedNutrition = calculatedNutrition; }
    
    public NutritionRecommendation getRecommendation() { return recommendation; }
    public void setRecommendation(NutritionRecommendation recommendation) { this.recommendation = recommendation; }
}
