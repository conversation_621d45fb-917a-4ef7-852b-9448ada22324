#!/usr/bin/env python3
"""
USDA FoodData Central 数据下载脚本
下载完整的食物营养数据库并准备MySQL导入
"""

import os
import requests
import zipfile
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class USDADataDownloader:
    def __init__(self, download_dir="./data"):
        self.download_dir = Path(download_dir)
        self.download_dir.mkdir(exist_ok=True)
        
        # USDA FoodData Central 最新数据下载链接 (2025年4月版本)
        self.download_urls = {
            "foundation_foods": {
                "csv": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_foundation_food_csv_2025-04.zip",
                "json": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_foundation_food_json_2025-04.zip"
            },
            "sr_legacy": {
                "csv": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_sr_legacy_food_csv_2018-04.zip",
                "json": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_sr_legacy_food_json_2018-04.zip"
            },
            "fndds": {
                "csv": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_survey_food_csv_2024-10.zip",
                "json": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_survey_food_json_2024-10.zip"
            },
            "branded": {
                "csv": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_branded_food_csv_2025-04.zip",
                "json": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_branded_food_json_2025-04.zip"
            },
            "full_download": {
                "csv": "https://fdc.nal.usda.gov/fdc-datasets/FoodData_Central_csv_2025-04.zip"
            }
        }

    def download_file(self, url, filename):
        """下载文件并显示进度"""
        file_path = self.download_dir / filename
        
        if file_path.exists():
            logger.info(f"文件已存在: {filename}")
            return file_path
            
        logger.info(f"开始下载: {filename}")
        logger.info(f"URL: {url}")
        
        try:
            response = requests.get(url, stream=True)
            response.raise_for_status()
            
            total_size = int(response.headers.get('content-length', 0))
            downloaded_size = 0
            
            with open(file_path, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)
                        downloaded_size += len(chunk)
                        
                        if total_size > 0:
                            progress = (downloaded_size / total_size) * 100
                            print(f"\r下载进度: {progress:.1f}% ({downloaded_size}/{total_size} bytes)", end='')
            
            print()  # 换行
            logger.info(f"下载完成: {filename}")
            return file_path
            
        except Exception as e:
            logger.error(f"下载失败 {filename}: {e}")
            return None

    def extract_zip(self, zip_path, extract_dir=None):
        """解压ZIP文件"""
        if extract_dir is None:
            extract_dir = self.download_dir / "extracted"
        
        extract_dir = Path(extract_dir)
        extract_dir.mkdir(exist_ok=True)
        
        logger.info(f"解压文件: {zip_path}")
        
        try:
            with zipfile.ZipFile(zip_path, 'r') as zip_ref:
                zip_ref.extractall(extract_dir)
            
            logger.info(f"解压完成到: {extract_dir}")
            return extract_dir
            
        except Exception as e:
            logger.error(f"解压失败: {e}")
            return None

    def download_full_dataset(self):
        """下载完整数据集"""
        logger.info("开始下载USDA FoodData Central完整数据集...")
        
        url = self.download_urls["full_download"]["csv"]
        filename = "FoodData_Central_csv_2025-04.zip"
        
        zip_path = self.download_file(url, filename)
        if zip_path:
            extract_dir = self.extract_zip(zip_path)
            return extract_dir
        return None

    def download_specific_dataset(self, dataset_type, format_type="csv"):
        """下载特定数据集"""
        if dataset_type not in self.download_urls:
            logger.error(f"未知的数据集类型: {dataset_type}")
            return None
            
        if format_type not in self.download_urls[dataset_type]:
            logger.error(f"数据集 {dataset_type} 不支持格式 {format_type}")
            return None
        
        url = self.download_urls[dataset_type][format_type]
        filename = f"{dataset_type}_{format_type}_2025-04.zip"
        
        zip_path = self.download_file(url, filename)
        if zip_path:
            extract_dir = self.extract_zip(zip_path)
            return extract_dir
        return None

    def list_csv_files(self, directory):
        """列出目录中的所有CSV文件"""
        csv_files = list(Path(directory).glob("**/*.csv"))
        logger.info(f"找到 {len(csv_files)} 个CSV文件:")
        for csv_file in csv_files:
            file_size = csv_file.stat().st_size / (1024 * 1024)  # MB
            logger.info(f"  - {csv_file.name} ({file_size:.1f} MB)")
        return csv_files

def main():
    """主函数"""
    downloader = USDADataDownloader()
    
    print("USDA FoodData Central 数据下载器")
    print("=" * 50)
    print("1. 下载完整数据集 (推荐, ~3GB)")
    print("2. 下载Foundation Foods (基础食物)")
    print("3. 下载SR Legacy (标准参考)")
    print("4. 下载FNDDS (膳食研究)")
    print("5. 下载Branded Foods (品牌食品)")
    
    choice = input("\n请选择下载选项 (1-5): ").strip()
    
    if choice == "1":
        extract_dir = downloader.download_full_dataset()
    elif choice == "2":
        extract_dir = downloader.download_specific_dataset("foundation_foods")
    elif choice == "3":
        extract_dir = downloader.download_specific_dataset("sr_legacy")
    elif choice == "4":
        extract_dir = downloader.download_specific_dataset("fndds")
    elif choice == "5":
        extract_dir = downloader.download_specific_dataset("branded")
    else:
        print("无效选择")
        return
    
    if extract_dir:
        print(f"\n数据已下载并解压到: {extract_dir}")
        csv_files = downloader.list_csv_files(extract_dir)
        print(f"\n接下来可以使用 mysql_import.py 脚本将这些CSV文件导入MySQL数据库")

if __name__ == "__main__":
    main()
