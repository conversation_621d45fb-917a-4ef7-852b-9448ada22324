package com.example.meals.mapper;

import com.example.meals.entity.Food;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 食物数据访问层
 */
@Mapper
public interface FoodMapper {

    /**
     * 查询所有食物
     */
    List<Food> selectAll();

    /**
     * 查询启用的食物
     */
    List<Food> selectEnabled();

    /**
     * 根据ID查询食物
     */
    Food selectById(Long id);

    /**
     * 根据名称查询食物
     */
    Food selectByName(String name);

    /**
     * 根据名称模糊查询食物
     */
    List<Food> selectByNameLike(@Param("name") String name);

    /**
     * 根据分类查询食物
     */
    List<Food> selectByCategory(String category);

    /**
     * 搜索食物（支持名称和分类）
     */
    List<Food> searchFoods(@Param("keyword") String keyword, @Param("category") String category);

    /**
     * 分页查询食物
     */
    List<Food> selectByPage(@Param("offset") int offset, 
                           @Param("limit") int limit,
                           @Param("keyword") String keyword,
                           @Param("category") String category,
                           @Param("status") Integer status);

    /**
     * 统计食物总数
     */
    int countFoods(@Param("keyword") String keyword,
                   @Param("category") String category,
                   @Param("status") Integer status);

    /**
     * 获取所有食物分类
     */
    List<String> selectAllCategories();

    /**
     * 插入食物
     */
    int insert(Food food);

    /**
     * 更新食物
     */
    int update(Food food);

    /**
     * 删除食物
     */
    int deleteById(Long id);

    /**
     * 检查名称是否存在（排除指定ID）
     */
    int countByNameExcludeId(@Param("name") String name, @Param("excludeId") Long excludeId);

    /**
     * 检查名称是否存在
     */
    int countByName(String name);

    /**
     * 批量插入食物
     */
    int batchInsert(@Param("foods") List<Food> foods);

    /**
     * 根据营养成分范围查询食物
     */
    List<Food> selectByNutritionRange(@Param("minCalories") Double minCalories,
                                     @Param("maxCalories") Double maxCalories,
                                     @Param("minProtein") Double minProtein,
                                     @Param("maxProtein") Double maxProtein,
                                     @Param("minFat") Double minFat,
                                     @Param("maxFat") Double maxFat,
                                     @Param("minCarbs") Double minCarbs,
                                     @Param("maxCarbs") Double maxCarbs);

    /**
     * 获取热门食物（按查询次数排序，需要后续添加查询统计功能）
     */
    List<Food> selectPopularFoods(@Param("limit") int limit);

    /**
     * 获取推荐食物（基于营养均衡）
     */
    List<Food> selectRecommendedFoods(@Param("limit") int limit);
}
