package com.example.meals.service.impl;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodResponse;
import com.example.meals.dto.PageResponse;
import com.example.meals.dto.request.FoodRequest;
import com.example.meals.dto.NutritionAnalysisResponse;
import com.example.meals.entity.Food;
import com.example.meals.mapper.FoodMapper;
import com.example.meals.service.FoodService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 食物服务实现类
 */
@Service
public class FoodServiceImpl implements FoodService {
    
    @Autowired
    private FoodMapper foodMapper;
    
    @Override
    public Result<List<FoodResponse>> getAllFoods() {
        try {
            List<Food> foods = foodMapper.selectAll();
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取食物列表失败");
        }
    }
    
    @Override
    public Result<List<FoodResponse>> getEnabledFoods() {
        try {
            List<Food> foods = foodMapper.selectEnabled();
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取启用食物列表失败");
        }
    }
    
    @Override
    public Result<FoodResponse> getFoodById(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("食物ID不能为空");
        }
        
        try {
            Food food = foodMapper.selectById(id);
            if (food == null) {
                return Result.notFound("食物不存在");
            }
            return Result.success(new FoodResponse(food));
        } catch (Exception e) {
            return Result.error("获取食物详情失败");
        }
    }
    
    @Override
    public Result<List<FoodResponse>> searchFoods(String keyword, String category) {
        try {
            List<Food> foods = foodMapper.searchFoods(keyword, category);
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("搜索食物失败");
        }
    }
    
    @Override
    public Result<PageResponse<FoodResponse>> getFoodsByPage(Integer page, Integer size, 
                                                           String keyword, String category, Integer status) {
        if (page == null || page < 1) page = 1;
        if (size == null || size < 1) size = 10;
        if (size > 100) size = 100; // 限制最大页面大小
        
        try {
            int offset = (page - 1) * size;
            
            // 查询数据
            List<Food> foods = foodMapper.selectByPage(offset, size, keyword, category, status);
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            
            // 查询总数
            int total = foodMapper.countFoods(keyword, category, status);
            
            PageResponse<FoodResponse> pageResponse = PageResponse.of(responses, (long) total, page, size);
            
            return Result.success(pageResponse);
        } catch (Exception e) {
            return Result.error("分页查询食物失败");
        }
    }
    
    @Override
    public Result<List<String>> getAllCategories() {
        try {
            List<String> categories = foodMapper.selectAllCategories();
            return Result.success(categories);
        } catch (Exception e) {
            return Result.error("获取食物分类失败");
        }
    }
    
    @Override
    public Result<FoodResponse> createFood(FoodRequest request) {
        // 验证请求参数
        Result<Void> validationResult = validateFoodRequest(request);
        if (!validationResult.getSuccess()) {
            return Result.badRequest(validationResult.getMessage());
        }
        
        // 检查名称是否已存在
        if (foodMapper.countByName(request.getName()) > 0) {
            return Result.badRequest("食物名称已存在");
        }
        
        try {
            Food food = convertToEntity(request);
            food.setCreateTime(LocalDateTime.now());
            food.setUpdateTime(LocalDateTime.now());
            
            int result = foodMapper.insert(food);
            if (result > 0) {
                return Result.success("创建成功", new FoodResponse(food));
            } else {
                return Result.error("创建失败");
            }
        } catch (Exception e) {
            return Result.error("创建食物失败");
        }
    }
    
    @Override
    public Result<FoodResponse> updateFood(Long id, FoodRequest request) {
        if (id == null || id <= 0) {
            return Result.badRequest("食物ID不能为空");
        }
        
        // 验证请求参数
        Result<Void> validationResult = validateFoodRequest(request);
        if (!validationResult.getSuccess()) {
            return Result.badRequest(validationResult.getMessage());
        }
        
        // 检查食物是否存在
        Food existingFood = foodMapper.selectById(id);
        if (existingFood == null) {
            return Result.notFound("食物不存在");
        }
        
        // 检查名称是否已被其他食物使用
        if (foodMapper.countByNameExcludeId(request.getName(), id) > 0) {
            return Result.badRequest("食物名称已存在");
        }
        
        try {
            Food food = convertToEntity(request);
            food.setId(id);
            food.setCreateTime(existingFood.getCreateTime());
            food.setUpdateTime(LocalDateTime.now());
            
            int result = foodMapper.update(food);
            if (result > 0) {
                return Result.success("更新成功", new FoodResponse(food));
            } else {
                return Result.error("更新失败");
            }
        } catch (Exception e) {
            return Result.error("更新食物失败");
        }
    }
    
    @Override
    public Result<Void> deleteFood(Long id) {
        if (id == null || id <= 0) {
            return Result.badRequest("食物ID不能为空");
        }
        
        // 检查食物是否存在
        Food food = foodMapper.selectById(id);
        if (food == null) {
            return Result.notFound("食物不存在");
        }
        
        try {
            int result = foodMapper.deleteById(id);
            if (result > 0) {
                return Result.success();
            } else {
                return Result.error("删除失败");
            }
        } catch (Exception e) {
            return Result.error("删除食物失败");
        }
    }
    
    @Override
    public Result<Boolean> checkFoodName(String name, Long excludeId) {
        if (!StringUtils.hasText(name)) {
            return Result.badRequest("食物名称不能为空");
        }
        
        try {
            int count;
            if (excludeId != null) {
                count = foodMapper.countByNameExcludeId(name, excludeId);
            } else {
                count = foodMapper.countByName(name);
            }
            return Result.success(count == 0);
        } catch (Exception e) {
            return Result.error("检查食物名称失败");
        }
    }
    
    @Override
    public Result<Integer> batchImportFoods(List<FoodRequest> requests) {
        if (requests == null || requests.isEmpty()) {
            return Result.badRequest("导入数据不能为空");
        }
        
        try {
            List<Food> foods = new ArrayList<>();
            LocalDateTime now = LocalDateTime.now();
            
            for (FoodRequest request : requests) {
                // 验证每个请求
                Result<Void> validationResult = validateFoodRequest(request);
                if (!validationResult.getSuccess()) {
                    return Result.badRequest("第" + (requests.indexOf(request) + 1) + "条数据：" + validationResult.getMessage());
                }
                
                Food food = convertToEntity(request);
                food.setCreateTime(now);
                food.setUpdateTime(now);
                foods.add(food);
            }
            
            int result = foodMapper.batchInsert(foods);
            return Result.success("成功导入" + result + "条数据", result);
        } catch (Exception e) {
            return Result.error("批量导入食物失败");
        }
    }

    @Override
    public Result<List<FoodResponse>> getFoodsByNutritionRange(Double minCalories, Double maxCalories,
                                                              Double minProtein, Double maxProtein,
                                                              Double minFat, Double maxFat,
                                                              Double minCarbs, Double maxCarbs) {
        try {
            List<Food> foods = foodMapper.selectByNutritionRange(
                    minCalories, maxCalories,
                    minProtein, maxProtein,
                    minFat, maxFat,
                    minCarbs, maxCarbs
            );
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("根据营养成分查询食物失败");
        }
    }

    @Override
    public Result<List<FoodResponse>> getPopularFoods(Integer limit) {
        if (limit == null || limit <= 0) limit = 10;
        if (limit > 50) limit = 50; // 限制最大数量

        try {
            List<Food> foods = foodMapper.selectPopularFoods(limit);
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取热门食物失败");
        }
    }

    @Override
    public Result<List<FoodResponse>> getRecommendedFoods(Integer limit) {
        if (limit == null || limit <= 0) limit = 10;
        if (limit > 50) limit = 50; // 限制最大数量

        try {
            List<Food> foods = foodMapper.selectRecommendedFoods(limit);
            List<FoodResponse> responses = foods.stream()
                    .map(FoodResponse::new)
                    .collect(Collectors.toList());
            return Result.success(responses);
        } catch (Exception e) {
            return Result.error("获取推荐食物失败");
        }
    }

    @Override
    public Result<NutritionAnalysisResponse> calculateNutrition(Long foodId, Double weight) {
        if (foodId == null || foodId <= 0) {
            return Result.badRequest("食物ID不能为空");
        }
        if (weight == null || weight <= 0) {
            return Result.badRequest("重量必须大于0");
        }
        if (weight > 10000) {
            return Result.badRequest("重量不能超过10000克");
        }

        try {
            System.out.println("开始营养分析计算...");
            // 获取食物信息
            Food food = foodMapper.selectById(foodId);
            if (food == null) {
                return Result.notFound("食物不存在");
            }
            System.out.println("食物信息获取成功: " + food.getName());

            // 计算营养成分
            double ratio = weight / 100.0; // 转换为100g的倍数
            System.out.println("计算比例: " + ratio);

            NutritionAnalysisResponse response = new NutritionAnalysisResponse();
            System.out.println("创建响应对象成功");

            response.setFood(new FoodResponse(food));
            System.out.println("设置食物信息成功");

            response.setWeight(weight);
            System.out.println("设置重量成功");

            // 计算营养成分
            System.out.println("开始计算营养成分...");
            NutritionAnalysisResponse.CalculatedNutrition calculated = new NutritionAnalysisResponse.CalculatedNutrition();
            System.out.println("创建计算营养对象成功");

            try {
                calculated.setCalories(safeCalculate(food.getCalories(), ratio));
                calculated.setProtein(safeCalculate(food.getProtein(), ratio));
                calculated.setFat(safeCalculate(food.getFat(), ratio));
                calculated.setCarbohydrates(safeCalculate(food.getCarbohydrates(), ratio));
                calculated.setFiber(safeCalculate(food.getFiber(), ratio));
                calculated.setSugar(safeCalculate(food.getSugar(), ratio));
                calculated.setSodium(safeCalculate(food.getSodium(), ratio));
                calculated.setCalcium(safeCalculate(food.getCalcium(), ratio));
                calculated.setIron(safeCalculate(food.getIron(), ratio));
                calculated.setVitaminC(safeCalculate(food.getVitaminC(), ratio));
                calculated.setVitaminA(safeCalculate(food.getVitaminA(), ratio));
                calculated.setVitaminD(safeCalculate(food.getVitaminD(), ratio));
                calculated.setVitaminE(safeCalculate(food.getVitaminE(), ratio));
                calculated.setVitaminB1(safeCalculate(food.getVitaminB1(), ratio));
                calculated.setVitaminB2(safeCalculate(food.getVitaminB2(), ratio));
                calculated.setVitaminB6(safeCalculate(food.getVitaminB6(), ratio));
                calculated.setVitaminB12(safeCalculate(food.getVitaminB12(), ratio));
                calculated.setFolicAcid(safeCalculate(food.getFolicAcid(), ratio));
                calculated.setPotassium(safeCalculate(food.getPotassium(), ratio));
                calculated.setMagnesium(safeCalculate(food.getMagnesium(), ratio));
                calculated.setZinc(safeCalculate(food.getZinc(), ratio));
                calculated.setPhosphorus(safeCalculate(food.getPhosphorus(), ratio));
            } catch (Exception e) {
                System.out.println("营养成分计算异常: " + e.getMessage());
                e.printStackTrace();
                throw e;
            }
            System.out.println("营养成分计算完成");

            response.setCalculatedNutrition(calculated);

            // 生成营养建议
            NutritionAnalysisResponse.NutritionRecommendation recommendation = generateNutritionRecommendation(calculated);
            response.setRecommendation(recommendation);

            return Result.success(response);
        } catch (Exception e) {
            return Result.error("营养分析计算失败");
        }
    }

    // ========== 私有辅助方法 ==========

    /**
     * 验证食物请求参数
     */
    private Result<Void> validateFoodRequest(FoodRequest request) {
        if (request == null) {
            return Result.badRequest("请求参数不能为空");
        }

        if (!StringUtils.hasText(request.getName())) {
            return Result.badRequest("食物名称不能为空");
        }

        if (request.getName().length() > 100) {
            return Result.badRequest("食物名称长度不能超过100个字符");
        }

        if (!StringUtils.hasText(request.getCategory())) {
            return Result.badRequest("食物分类不能为空");
        }

        if (request.getCategory().length() > 50) {
            return Result.badRequest("食物分类长度不能超过50个字符");
        }

        // 验证营养成分数值
        if (request.getCalories() != null && (request.getCalories() < 0 || request.getCalories() > 9999)) {
            return Result.badRequest("热量值必须在0-9999之间");
        }

        if (request.getProtein() != null && (request.getProtein() < 0 || request.getProtein() > 999)) {
            return Result.badRequest("蛋白质含量必须在0-999之间");
        }

        if (request.getFat() != null && (request.getFat() < 0 || request.getFat() > 999)) {
            return Result.badRequest("脂肪含量必须在0-999之间");
        }

        if (request.getCarbohydrates() != null && (request.getCarbohydrates() < 0 || request.getCarbohydrates() > 999)) {
            return Result.badRequest("碳水化合物含量必须在0-999之间");
        }

        return Result.success();
    }

    /**
     * 将请求DTO转换为实体对象
     */
    private Food convertToEntity(FoodRequest request) {
        Food food = new Food();
        food.setName(request.getName());
        food.setCategory(request.getCategory());
        food.setDescription(request.getDescription());
        food.setStatus(request.getStatus() != null ? request.getStatus() : 1);

        // 设置营养成分
        food.setCalories(request.getCalories());
        food.setProtein(request.getProtein());
        food.setFat(request.getFat());
        food.setCarbohydrates(request.getCarbohydrates());
        food.setFiber(request.getFiber());
        food.setSugar(request.getSugar());
        food.setSodium(request.getSodium());
        food.setCalcium(request.getCalcium());
        food.setIron(request.getIron());
        food.setVitaminC(request.getVitaminC());
        food.setVitaminA(request.getVitaminA());
        food.setVitaminD(request.getVitaminD());
        food.setVitaminE(request.getVitaminE());
        food.setVitaminB1(request.getVitaminB1());
        food.setVitaminB2(request.getVitaminB2());
        food.setVitaminB6(request.getVitaminB6());
        food.setVitaminB12(request.getVitaminB12());
        food.setFolicAcid(request.getFolicAcid());
        food.setPotassium(request.getPotassium());
        food.setMagnesium(request.getMagnesium());
        food.setZinc(request.getZinc());
        food.setPhosphorus(request.getPhosphorus());

        return food;
    }

    /**
     * 安全计算营养成分
     */
    private Double safeCalculate(Double value, double ratio) {
        if (value == null) return 0.0;
        try {
            double result = value * ratio;
            return Math.round(result * 100.0) / 100.0;
        } catch (Exception e) {
            System.out.println("计算异常: value=" + value + ", ratio=" + ratio + ", error=" + e.getMessage());
            return 0.0;
        }
    }

    /**
     * 保留两位小数
     */
    private Double roundToTwo(Double value) {
        if (value == null) return 0.0;
        return Math.round(value * 100.0) / 100.0;
    }

    /**
     * 生成营养建议
     */
    private NutritionAnalysisResponse.NutritionRecommendation generateNutritionRecommendation(
            NutritionAnalysisResponse.CalculatedNutrition nutrition) {

        NutritionAnalysisResponse.NutritionRecommendation recommendation =
                new NutritionAnalysisResponse.NutritionRecommendation();

        List<String> healthTips = new ArrayList<>();

        // 热量评估
        if (nutrition.getCalories() != null) {
            if (nutrition.getCalories() < 50) {
                recommendation.setCaloriesLevel("低");
                healthTips.add("热量较低，适合减重期间食用");
            } else if (nutrition.getCalories() < 200) {
                recommendation.setCaloriesLevel("中");
                healthTips.add("热量适中，可作为日常饮食的一部分");
            } else {
                recommendation.setCaloriesLevel("高");
                healthTips.add("热量较高，建议适量食用");
            }
        }

        // 蛋白质评估
        if (nutrition.getProtein() != null) {
            if (nutrition.getProtein() < 5) {
                recommendation.setProteinLevel("低");
            } else if (nutrition.getProtein() < 15) {
                recommendation.setProteinLevel("中");
                healthTips.add("含有适量蛋白质，有助于肌肉健康");
            } else {
                recommendation.setProteinLevel("高");
                healthTips.add("富含蛋白质，是优质蛋白质来源");
            }
        }

        // 脂肪评估
        if (nutrition.getFat() != null) {
            if (nutrition.getFat() < 3) {
                recommendation.setFatLevel("低");
                healthTips.add("低脂肪食物，适合控制脂肪摄入");
            } else if (nutrition.getFat() < 10) {
                recommendation.setFatLevel("中");
            } else {
                recommendation.setFatLevel("高");
                healthTips.add("脂肪含量较高，建议适量食用");
            }
        }

        // 碳水化合物评估
        if (nutrition.getCarbohydrates() != null) {
            if (nutrition.getCarbohydrates() < 10) {
                recommendation.setCarbsLevel("低");
                healthTips.add("低碳水化合物，适合低碳饮食");
            } else if (nutrition.getCarbohydrates() < 30) {
                recommendation.setCarbsLevel("中");
            } else {
                recommendation.setCarbsLevel("高");
                healthTips.add("富含碳水化合物，可提供能量");
            }
        }

        // 膳食纤维评估
        if (nutrition.getFiber() != null) {
            if (nutrition.getFiber() < 2) {
                recommendation.setFiberLevel("低");
            } else if (nutrition.getFiber() < 5) {
                recommendation.setFiberLevel("中");
                healthTips.add("含有膳食纤维，有助于消化健康");
            } else {
                recommendation.setFiberLevel("高");
                healthTips.add("富含膳食纤维，有助于肠道健康");
            }
        }

        // 钠含量评估
        if (nutrition.getSodium() != null) {
            if (nutrition.getSodium() < 100) {
                recommendation.setSodiumLevel("低");
                healthTips.add("低钠食物，适合控制钠摄入");
            } else if (nutrition.getSodium() < 400) {
                recommendation.setSodiumLevel("中");
            } else {
                recommendation.setSodiumLevel("高");
                healthTips.add("钠含量较高，高血压患者需注意");
            }
        }

        // 添加通用健康建议
        healthTips.add("建议搭配多样化食物，保持营养均衡");
        healthTips.add("适量运动，保持健康生活方式");

        recommendation.setHealthTips(healthTips);

        return recommendation;
    }
}
