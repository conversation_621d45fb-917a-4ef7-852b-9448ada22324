import { apiRequest } from './auth'

// API基础URL
const API_BASE_URL = 'http://localhost:8080'

// 食物相关接口类型定义
export interface FoodItem {
  id: number
  name: string
  category: string
  description?: string
  status: number
  statusName: string
  nutrition: {
    calories: number
    protein: number
    fat: number
    carbohydrates: number
    fiber: number
    sugar: number
    sodium: number
    calcium: number
    iron: number
    vitaminC: number
    vitaminA?: number
    vitaminD?: number
    vitaminE?: number
    vitaminB1?: number
    vitaminB2?: number
    vitaminB6?: number
    vitaminB12?: number
    folicAcid?: number
    potassium?: number
    magnesium?: number
    zinc?: number
    phosphorus?: number
  }
  createTime: string
  updateTime: string
}

export interface NutritionAnalysis {
  food: FoodItem
  weight: number
  calculatedNutrition: {
    calories: number
    protein: number
    fat: number
    carbohydrates: number
    fiber: number
    sugar: number
    sodium: number
    calcium: number
    iron: number
    vitaminC: number
    vitaminA?: number
    vitaminD?: number
    vitaminE?: number
    vitaminB1?: number
    vitaminB2?: number
    vitaminB6?: number
    vitaminB12?: number
    folicAcid?: number
    potassium?: number
    magnesium?: number
    zinc?: number
    phosphorus?: number
  }
  recommendation: {
    caloriesLevel: string
    proteinLevel: string
    fatLevel: string
    carbsLevel: string
    fiberLevel: string
    sodiumLevel: string
    healthTips: string[]
  }
}

export interface FoodRequest {
  name: string
  category: string
  description?: string
  status?: number
  calories?: number
  protein?: number
  fat?: number
  carbohydrates?: number
  fiber?: number
  sugar?: number
  sodium?: number
  calcium?: number
  iron?: number
  vitaminC?: number
  vitaminA?: number
  vitaminD?: number
  vitaminE?: number
  vitaminB1?: number
  vitaminB2?: number
  vitaminB6?: number
  vitaminB12?: number
  folicAcid?: number
  potassium?: number
  magnesium?: number
  zinc?: number
  phosphorus?: number
}

export interface PageResponse<T> {
  records: T[]
  total: number
  page: number
  size: number
  pages: number
}

// 食物API函数

/**
 * 获取启用的食物列表
 */
export const getEnabledFoods = () => {
  return apiRequest<FoodItem[]>(`${API_BASE_URL}/api/foods/enabled`)
}

/**
 * 搜索食物
 */
export const searchFoods = (keyword?: string, category?: string) => {
  const params = new URLSearchParams()
  if (keyword) params.append('keyword', keyword)
  if (category) params.append('category', category)

  const url = `${API_BASE_URL}/api/foods/search${params.toString() ? '?' + params.toString() : ''}`
  return apiRequest<FoodItem[]>(url)
}

/**
 * 根据ID获取食物详情
 */
export const getFoodById = (id: number) => {
  return apiRequest<FoodItem>(`${API_BASE_URL}/api/foods/${id}`)
}

/**
 * 获取所有食物分类
 */
export const getAllCategories = () => {
  return apiRequest<string[]>(`${API_BASE_URL}/api/foods/categories`)
}

/**
 * 营养分析计算
 */
export const analyzeNutrition = (foodId: number, weight: number) => {
  const url = `${API_BASE_URL}/api/foods/${foodId}/analyze?weight=${weight}`
  return apiRequest<NutritionAnalysis>(url, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({})
  })
}

/**
 * 获取热门食物
 */
export const getPopularFoods = (limit: number = 10) => {
  return apiRequest<FoodItem[]>(`${API_BASE_URL}/api/foods/popular?limit=${limit}`)
}

/**
 * 获取推荐食物
 */
export const getRecommendedFoods = (limit: number = 10) => {
  return apiRequest<FoodItem[]>(`${API_BASE_URL}/api/foods/recommended?limit=${limit}`)
}

/**
 * 根据营养成分范围查询食物
 */
export const getFoodsByNutritionRange = (params: {
  minCalories?: number
  maxCalories?: number
  minProtein?: number
  maxProtein?: number
  minFat?: number
  maxFat?: number
  minCarbs?: number
  maxCarbs?: number
}) => {
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) searchParams.append(key, value.toString())
  })

  const url = `${API_BASE_URL}/api/foods/nutrition-range${searchParams.toString() ? '?' + searchParams.toString() : ''}`
  return apiRequest<FoodItem[]>(url)
}

// ========== 管理员功能 ==========

/**
 * 获取所有食物（管理员）
 */
export const getAllFoods = () => {
  return apiRequest<FoodItem[]>(`${API_BASE_URL}/api/foods/admin/all`)
}

/**
 * 分页查询食物（管理员）
 */
export const getFoodsByPage = (params: {
  page?: number
  size?: number
  keyword?: string
  category?: string
  status?: number
}) => {
  const searchParams = new URLSearchParams()
  Object.entries(params).forEach(([key, value]) => {
    if (value !== undefined) searchParams.append(key, value.toString())
  })

  const url = `${API_BASE_URL}/api/foods/admin/page${searchParams.toString() ? '?' + searchParams.toString() : ''}`
  return apiRequest<PageResponse<FoodItem>>(url)
}

/**
 * 创建食物（管理员）
 */
export const createFood = (data: FoodRequest) => {
  return apiRequest<FoodItem>(`${API_BASE_URL}/api/foods/admin/create`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
}

/**
 * 更新食物（管理员）
 */
export const updateFood = (id: number, data: FoodRequest) => {
  return apiRequest<FoodItem>(`${API_BASE_URL}/api/foods/admin/${id}`, {
    method: 'PUT',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
}

/**
 * 删除食物（管理员）
 */
export const deleteFood = (id: number) => {
  return apiRequest<void>(`${API_BASE_URL}/api/foods/admin/${id}`, { method: 'DELETE' })
}

/**
 * 检查食物名称是否可用（管理员）
 */
export const checkFoodName = (name: string, excludeId?: number) => {
  const params = new URLSearchParams({ name })
  if (excludeId) params.append('excludeId', excludeId.toString())

  return apiRequest<boolean>(`${API_BASE_URL}/api/foods/admin/check-name?${params.toString()}`)
}

/**
 * 批量导入食物（管理员）
 */
export const batchImportFoods = (data: FoodRequest[]) => {
  return apiRequest<number>(`${API_BASE_URL}/api/foods/admin/batch-import`, {
    method: 'POST',
    headers: { 'Content-Type': 'application/json' },
    body: JSON.stringify(data)
  })
}

// 工具函数

/**
 * 格式化营养成分显示
 */
export const formatNutritionValue = (value: number | undefined, unit: string): string => {
  if (value === undefined || value === null) return '-'
  if (value === 0) return '0' + unit
  
  // 根据数值大小决定小数位数
  if (value < 1) {
    return value.toFixed(2) + unit
  } else if (value < 10) {
    return value.toFixed(1) + unit
  } else {
    return Math.round(value) + unit
  }
}

/**
 * 获取营养成分等级颜色
 */
export const getNutritionLevelColor = (level: string): string => {
  switch (level) {
    case '低': return '#52c41a' // 绿色
    case '中': return '#faad14' // 橙色
    case '高': return '#f5222d' // 红色
    default: return '#d9d9d9' // 灰色
  }
}

/**
 * 计算营养密度评分
 */
export const calculateNutritionScore = (nutrition: FoodItem['nutrition']): number => {
  let score = 0
  
  // 蛋白质评分 (0-30分)
  if (nutrition.protein >= 20) score += 30
  else if (nutrition.protein >= 10) score += 20
  else if (nutrition.protein >= 5) score += 10
  
  // 膳食纤维评分 (0-25分)
  if (nutrition.fiber >= 10) score += 25
  else if (nutrition.fiber >= 5) score += 15
  else if (nutrition.fiber >= 2) score += 10
  
  // 维生素C评分 (0-20分)
  if (nutrition.vitaminC >= 50) score += 20
  else if (nutrition.vitaminC >= 20) score += 15
  else if (nutrition.vitaminC >= 5) score += 10
  
  // 钙质评分 (0-15分)
  if (nutrition.calcium >= 200) score += 15
  else if (nutrition.calcium >= 100) score += 10
  else if (nutrition.calcium >= 50) score += 5
  
  // 铁质评分 (0-10分)
  if (nutrition.iron >= 5) score += 10
  else if (nutrition.iron >= 2) score += 5
  else if (nutrition.iron >= 1) score += 3
  
  // 扣分项
  // 高钠扣分
  if (nutrition.sodium > 1000) score -= 20
  else if (nutrition.sodium > 500) score -= 10
  
  // 高糖扣分
  if (nutrition.sugar > 20) score -= 15
  else if (nutrition.sugar > 10) score -= 8
  
  return Math.max(0, Math.min(100, score))
}
