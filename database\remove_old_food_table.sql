-- 删除旧的food表及相关数据的SQL脚本
-- 警告：此操作不可逆，请在执行前备份数据库

USE meals;

-- 1. 检查是否存在food表的外键依赖
SELECT 
    TABLE_NAME,
    COLUMN_NAME,
    CONSTRAINT_NAME,
    REFERENCED_TABLE_NAME,
    REFERENCED_COLUMN_NAME
FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE 
WHERE REFERENCED_TABLE_SCHEMA = 'meals' 
AND REFERENCED_TABLE_NAME = 'food';

-- 2. 删除可能存在的外键约束
-- 注意：如果有其他表引用food表，需要先删除这些外键约束

-- 3. 删除food表
DROP TABLE IF EXISTS `food`;

-- 4. 验证删除结果
SELECT TABLE_NAME 
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'meals' 
AND TABLE_NAME = 'food';

-- 如果上述查询返回空结果，说明food表已成功删除

-- 5. 清理可能存在的相关索引（如果有的话）
-- 由于删除表时索引会自动删除，这里主要是确认

-- 6. 显示当前数据库中的所有表
SHOW TABLES;

-- 执行完成后的说明：
-- 1. food表及其所有数据已被永久删除
-- 2. 相关的索引和约束也已被删除
-- 3. 现在可以安全地使用USDA食物数据表
-- 4. 建议更新应用程序代码以使用新的USDA表结构
