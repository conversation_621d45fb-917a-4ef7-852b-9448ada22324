package com.example.meals.service;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodResponse;
import com.example.meals.dto.request.FoodRequest;
import com.example.meals.dto.PageResponse;
import com.example.meals.dto.NutritionAnalysisResponse;

import java.util.List;

/**
 * 食物服务接口
 */
public interface FoodService {
    
    /**
     * 获取所有食物
     */
    Result<List<FoodResponse>> getAllFoods();
    
    /**
     * 获取启用的食物
     */
    Result<List<FoodResponse>> getEnabledFoods();
    
    /**
     * 根据ID获取食物
     */
    Result<FoodResponse> getFoodById(Long id);
    
    /**
     * 搜索食物
     */
    Result<List<FoodResponse>> searchFoods(String keyword, String category);
    
    /**
     * 分页查询食物
     */
    Result<PageResponse<FoodResponse>> getFoodsByPage(Integer page, Integer size,
                                                     String keyword, String category, Integer status);
    
    /**
     * 获取所有食物分类
     */
    Result<List<String>> getAllCategories();
    
    /**
     * 创建食物
     */
    Result<FoodResponse> createFood(FoodRequest request);
    
    /**
     * 更新食物
     */
    Result<FoodResponse> updateFood(Long id, FoodRequest request);
    
    /**
     * 删除食物
     */
    Result<Void> deleteFood(Long id);
    
    /**
     * 检查食物名称是否可用
     */
    Result<Boolean> checkFoodName(String name, Long excludeId);
    
    /**
     * 批量导入食物
     */
    Result<Integer> batchImportFoods(List<FoodRequest> requests);
    
    /**
     * 根据营养成分范围查询食物
     */
    Result<List<FoodResponse>> getFoodsByNutritionRange(Double minCalories, Double maxCalories,
                                                       Double minProtein, Double maxProtein,
                                                       Double minFat, Double maxFat,
                                                       Double minCarbs, Double maxCarbs);
    
    /**
     * 获取热门食物
     */
    Result<List<FoodResponse>> getPopularFoods(Integer limit);
    
    /**
     * 获取推荐食物
     */
    Result<List<FoodResponse>> getRecommendedFoods(Integer limit);
    
    /**
     * 营养分析计算
     */
    Result<NutritionAnalysisResponse> calculateNutrition(Long foodId, Double weight);

}
