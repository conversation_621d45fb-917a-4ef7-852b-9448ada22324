<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.example.meals.mapper.FoodMapper">

    <!-- 结果映射 -->
    <resultMap id="FoodResultMap" type="com.example.meals.entity.Food">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="category" column="category"/>
        <result property="description" column="description"/>
        <result property="status" column="status"/>
        <result property="calories" column="calories"/>
        <result property="protein" column="protein"/>
        <result property="fat" column="fat"/>
        <result property="carbohydrates" column="carbohydrates"/>
        <result property="fiber" column="fiber"/>
        <result property="sugar" column="sugar"/>
        <result property="sodium" column="sodium"/>
        <result property="calcium" column="calcium"/>
        <result property="iron" column="iron"/>
        <result property="vitaminC" column="vitamin_c"/>
        <result property="vitaminA" column="vitamin_a"/>
        <result property="vitaminD" column="vitamin_d"/>
        <result property="vitaminE" column="vitamin_e"/>
        <result property="vitaminB1" column="vitamin_b1"/>
        <result property="vitaminB2" column="vitamin_b2"/>
        <result property="vitaminB6" column="vitamin_b6"/>
        <result property="vitaminB12" column="vitamin_b12"/>
        <result property="folicAcid" column="folic_acid"/>
        <result property="potassium" column="potassium"/>
        <result property="magnesium" column="magnesium"/>
        <result property="zinc" column="zinc"/>
        <result property="phosphorus" column="phosphorus"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <!-- 基础字段 -->
    <sql id="Base_Column_List">
        id, name, category, description, status,
        calories, protein, fat, carbohydrates, fiber, sugar,
        sodium, calcium, iron, vitamin_c, vitamin_a, vitamin_d, vitamin_e,
        vitamin_b1, vitamin_b2, vitamin_b6, vitamin_b12, folic_acid,
        potassium, magnesium, zinc, phosphorus,
        create_time, update_time
    </sql>

    <!-- 查询所有食物 -->
    <select id="selectAll" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        ORDER BY create_time DESC
    </select>

    <!-- 查询启用的食物 -->
    <select id="selectEnabled" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE status = 1
        ORDER BY create_time DESC
    </select>

    <!-- 根据ID查询食物 -->
    <select id="selectById" parameterType="long" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE id = #{id}
    </select>

    <!-- 根据名称查询食物 -->
    <select id="selectByName" parameterType="string" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE name = #{name}
        LIMIT 1
    </select>

    <!-- 根据名称模糊查询食物 -->
    <select id="selectByNameLike" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE name LIKE CONCAT('%', #{name}, '%')
        AND status = 1
        ORDER BY name
        LIMIT 20
    </select>

    <!-- 根据分类查询食物 -->
    <select id="selectByCategory" parameterType="string" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE category = #{category}
        AND status = 1
        ORDER BY name
    </select>

    <!-- 搜索食物 -->
    <select id="searchFoods" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE status = 1
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR category LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        ORDER BY name
        LIMIT 50
    </select>

    <!-- 分页查询食物 -->
    <select id="selectByPage" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR category LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
        ORDER BY create_time DESC
        LIMIT #{offset}, #{limit}
    </select>

    <!-- 统计食物总数 -->
    <select id="countFoods" resultType="int">
        SELECT COUNT(*)
        FROM food
        WHERE 1=1
        <if test="keyword != null and keyword != ''">
            AND (name LIKE CONCAT('%', #{keyword}, '%') 
                 OR category LIKE CONCAT('%', #{keyword}, '%')
                 OR description LIKE CONCAT('%', #{keyword}, '%'))
        </if>
        <if test="category != null and category != ''">
            AND category = #{category}
        </if>
        <if test="status != null">
            AND status = #{status}
        </if>
    </select>

    <!-- 获取所有食物分类 -->
    <select id="selectAllCategories" resultType="string">
        SELECT DISTINCT category
        FROM food
        WHERE status = 1
        ORDER BY category
    </select>

    <!-- 插入食物 -->
    <insert id="insert" parameterType="com.example.meals.entity.Food" useGeneratedKeys="true" keyProperty="id">
        INSERT INTO food (
            name, category, description, status,
            calories, protein, fat, carbohydrates, fiber, sugar,
            sodium, calcium, iron, vitamin_c, vitamin_a, vitamin_d, vitamin_e,
            vitamin_b1, vitamin_b2, vitamin_b6, vitamin_b12, folic_acid,
            potassium, magnesium, zinc, phosphorus,
            create_time, update_time
        ) VALUES (
            #{name}, #{category}, #{description}, #{status},
            #{calories}, #{protein}, #{fat}, #{carbohydrates}, #{fiber}, #{sugar},
            #{sodium}, #{calcium}, #{iron}, #{vitaminC}, #{vitaminA}, #{vitaminD}, #{vitaminE},
            #{vitaminB1}, #{vitaminB2}, #{vitaminB6}, #{vitaminB12}, #{folicAcid},
            #{potassium}, #{magnesium}, #{zinc}, #{phosphorus},
            #{createTime}, #{updateTime}
        )
    </insert>

    <!-- 更新食物 -->
    <update id="update" parameterType="com.example.meals.entity.Food">
        UPDATE food SET
            name = #{name},
            category = #{category},
            description = #{description},
            status = #{status},
            calories = #{calories},
            protein = #{protein},
            fat = #{fat},
            carbohydrates = #{carbohydrates},
            fiber = #{fiber},
            sugar = #{sugar},
            sodium = #{sodium},
            calcium = #{calcium},
            iron = #{iron},
            vitamin_c = #{vitaminC},
            vitamin_a = #{vitaminA},
            vitamin_d = #{vitaminD},
            vitamin_e = #{vitaminE},
            vitamin_b1 = #{vitaminB1},
            vitamin_b2 = #{vitaminB2},
            vitamin_b6 = #{vitaminB6},
            vitamin_b12 = #{vitaminB12},
            folic_acid = #{folicAcid},
            potassium = #{potassium},
            magnesium = #{magnesium},
            zinc = #{zinc},
            phosphorus = #{phosphorus},
            update_time = #{updateTime}
        WHERE id = #{id}
    </update>

    <!-- 删除食物 -->
    <delete id="deleteById" parameterType="long">
        DELETE FROM food WHERE id = #{id}
    </delete>

    <!-- 检查名称是否存在（排除指定ID） -->
    <select id="countByNameExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM food
        WHERE name = #{name}
        AND id != #{excludeId}
    </select>

    <!-- 检查名称是否存在 -->
    <select id="countByName" parameterType="string" resultType="int">
        SELECT COUNT(*)
        FROM food
        WHERE name = #{name}
    </select>

    <!-- 批量插入食物 -->
    <insert id="batchInsert" parameterType="list">
        INSERT INTO food (
            name, category, description, status,
            calories, protein, fat, carbohydrates, fiber, sugar,
            sodium, calcium, iron, vitamin_c, vitamin_a, vitamin_d, vitamin_e,
            vitamin_b1, vitamin_b2, vitamin_b6, vitamin_b12, folic_acid,
            potassium, magnesium, zinc, phosphorus,
            create_time, update_time
        ) VALUES
        <foreach collection="foods" item="food" separator=",">
        (
            #{food.name}, #{food.category}, #{food.description}, #{food.status},
            #{food.calories}, #{food.protein}, #{food.fat}, #{food.carbohydrates}, #{food.fiber}, #{food.sugar},
            #{food.sodium}, #{food.calcium}, #{food.iron}, #{food.vitaminC}, #{food.vitaminA}, #{food.vitaminD}, #{food.vitaminE},
            #{food.vitaminB1}, #{food.vitaminB2}, #{food.vitaminB6}, #{food.vitaminB12}, #{food.folicAcid},
            #{food.potassium}, #{food.magnesium}, #{food.zinc}, #{food.phosphorus},
            #{food.createTime}, #{food.updateTime}
        )
        </foreach>
    </insert>

    <!-- 根据营养成分范围查询食物 -->
    <select id="selectByNutritionRange" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE status = 1
        <if test="minCalories != null">
            AND calories &gt;= #{minCalories}
        </if>
        <if test="maxCalories != null">
            AND calories &lt;= #{maxCalories}
        </if>
        <if test="minProtein != null">
            AND protein &gt;= #{minProtein}
        </if>
        <if test="maxProtein != null">
            AND protein &lt;= #{maxProtein}
        </if>
        <if test="minFat != null">
            AND fat &gt;= #{minFat}
        </if>
        <if test="maxFat != null">
            AND fat &lt;= #{maxFat}
        </if>
        <if test="minCarbs != null">
            AND carbohydrates &gt;= #{minCarbs}
        </if>
        <if test="maxCarbs != null">
            AND carbohydrates &lt;= #{maxCarbs}
        </if>
        ORDER BY name
        LIMIT 50
    </select>

    <!-- 获取热门食物（暂时按创建时间排序，后续可添加查询统计功能） -->
    <select id="selectPopularFoods" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE status = 1
        ORDER BY create_time DESC
        LIMIT #{limit}
    </select>

    <!-- 获取推荐食物（基于营养均衡） -->
    <select id="selectRecommendedFoods" resultMap="FoodResultMap">
        SELECT <include refid="Base_Column_List"/>
        FROM food
        WHERE status = 1
        AND calories IS NOT NULL
        AND protein IS NOT NULL
        AND fat IS NOT NULL
        AND carbohydrates IS NOT NULL
        AND fiber IS NOT NULL
        ORDER BY (
            CASE
                WHEN calories BETWEEN 50 AND 200 THEN 1 ELSE 0 END +
            CASE
                WHEN protein &gt;= 5 THEN 1 ELSE 0 END +
            CASE
                WHEN fiber &gt;= 2 THEN 1 ELSE 0 END +
            CASE
                WHEN sodium &lt;= 400 THEN 1 ELSE 0 END
        ) DESC, name
        LIMIT #{limit}
    </select>

</mapper>
