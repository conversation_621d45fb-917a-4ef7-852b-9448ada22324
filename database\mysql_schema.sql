-- USDA FoodData Central MySQL数据库架构
-- 基于USDA FoodData Central数据结构设计

CREATE DATABASE IF NOT EXISTS food_nutrition_db CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE food_nutrition_db;

-- 食物表 (主表)
CREATE TABLE foods (
    fdc_id INT PRIMARY KEY,
    data_type VARCHAR(50) NOT NULL,
    description TEXT NOT NULL,
    food_category_id INT,
    publication_date DATE,
    INDEX idx_data_type (data_type),
    INDEX idx_food_category (food_category_id),
    INDEX idx_description (description(100))
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 食物类别表
CREATE TABLE food_category (
    id INT PRIMARY KEY,
    code VARCHAR(10),
    description VARCHAR(255) NOT NULL,
    INDEX idx_code (code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 营养素表
CREATE TABLE nutrient (
    id INT PRIMARY KEY,
    number VARCHAR(10),
    name VARCHAR(255) NOT NULL,
    unit_name VARCHAR(50),
    nutrient_nbr DECIMAL(10,3),
    rank INT,
    INDEX idx_number (number),
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 食物营养素含量表 (核心数据表)
CREATE TABLE food_nutrient (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fdc_id INT NOT NULL,
    nutrient_id INT NOT NULL,
    amount DECIMAL(15,6),
    data_points INT,
    derivation_id INT,
    min_value DECIMAL(15,6),
    max_value DECIMAL(15,6),
    median_value DECIMAL(15,6),
    footnote_id INT,
    min_year_acquired INT,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    FOREIGN KEY (nutrient_id) REFERENCES nutrient(id),
    INDEX idx_fdc_nutrient (fdc_id, nutrient_id),
    INDEX idx_nutrient (nutrient_id),
    INDEX idx_amount (amount)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 食物属性表
CREATE TABLE food_attribute (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fdc_id INT NOT NULL,
    seq_num INT,
    food_attribute_type_id INT,
    name VARCHAR(255),
    value VARCHAR(255),
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_type (food_attribute_type_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 食物属性类型表
CREATE TABLE food_attribute_type (
    id INT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 品牌食品表 (扩展信息)
CREATE TABLE branded_food (
    fdc_id INT PRIMARY KEY,
    brand_owner VARCHAR(255),
    brand_name VARCHAR(255),
    subbrand_name VARCHAR(255),
    gtin_upc VARCHAR(50),
    ingredients TEXT,
    not_a_significant_source_of TEXT,
    serving_size DECIMAL(10,3),
    serving_size_unit VARCHAR(50),
    household_serving_fulltext VARCHAR(255),
    branded_food_category VARCHAR(255),
    data_source VARCHAR(100),
    package_weight VARCHAR(50),
    modified_date DATE,
    available_date DATE,
    market_country VARCHAR(100),
    discontinued_date DATE,
    preparation_state_code VARCHAR(50),
    trade_channel VARCHAR(100),
    short_description TEXT,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_brand_owner (brand_owner),
    INDEX idx_gtin_upc (gtin_upc),
    INDEX idx_category (branded_food_category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 基础食品表 (Foundation Foods)
CREATE TABLE foundation_food (
    fdc_id INT PRIMARY KEY,
    NDB_number INT,
    footnote_id INT,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_ndb_number (NDB_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- SR Legacy食品表
CREATE TABLE sr_legacy_food (
    fdc_id INT PRIMARY KEY,
    NDB_number INT,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_ndb_number (NDB_number)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 调查食品表 (FNDDS)
CREATE TABLE survey_fndds_food (
    fdc_id INT PRIMARY KEY,
    food_code INT,
    wweia_category_code INT,
    start_date DATE,
    end_date DATE,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_food_code (food_code),
    INDEX idx_wweia_category (wweia_category_code)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 测量单位表
CREATE TABLE measure_unit (
    id INT PRIMARY KEY,
    name VARCHAR(100) NOT NULL,
    INDEX idx_name (name)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 食物分量表
CREATE TABLE food_portion (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fdc_id INT NOT NULL,
    seq_num INT,
    amount DECIMAL(10,3),
    measure_unit_id INT,
    portion_description VARCHAR(255),
    modifier VARCHAR(255),
    gram_weight DECIMAL(10,3),
    data_points INT,
    footnote_id INT,
    min_year_acquired INT,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    FOREIGN KEY (measure_unit_id) REFERENCES measure_unit(id),
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_measure_unit (measure_unit_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 营养素推导表
CREATE TABLE nutrient_incoming_name (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    nutrient_id INT,
    FOREIGN KEY (nutrient_id) REFERENCES nutrient(id),
    INDEX idx_name (name),
    INDEX idx_nutrient_id (nutrient_id)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 脚注表
CREATE TABLE food_update_log_entry (
    id INT AUTO_INCREMENT PRIMARY KEY,
    fdc_id INT NOT NULL,
    available_date DATE,
    brand_owner VARCHAR(255),
    data_source VARCHAR(100),
    data_type VARCHAR(50),
    description TEXT,
    food_class VARCHAR(100),
    gtinUpc VARCHAR(50),
    household_serving_fulltext VARCHAR(255),
    ingredients TEXT,
    modified_date DATE,
    publication_date DATE,
    serving_size DECIMAL(10,3),
    serving_size_unit VARCHAR(50),
    branded_food_category VARCHAR(255),
    changes VARCHAR(500),
    food_attributes TEXT,
    FOREIGN KEY (fdc_id) REFERENCES foods(fdc_id) ON DELETE CASCADE,
    INDEX idx_fdc_id (fdc_id),
    INDEX idx_data_type (data_type)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

-- 创建外键约束
ALTER TABLE foods ADD FOREIGN KEY (food_category_id) REFERENCES food_category(id);
ALTER TABLE food_attribute ADD FOREIGN KEY (food_attribute_type_id) REFERENCES food_attribute_type(id);

-- 创建常用查询的视图
CREATE VIEW food_with_nutrients AS
SELECT 
    f.fdc_id,
    f.description as food_description,
    f.data_type,
    fc.description as category,
    n.name as nutrient_name,
    n.unit_name,
    fn.amount as nutrient_amount
FROM foods f
LEFT JOIN food_category fc ON f.food_category_id = fc.id
LEFT JOIN food_nutrient fn ON f.fdc_id = fn.fdc_id
LEFT JOIN nutrient n ON fn.nutrient_id = n.id;

-- 创建品牌食品详细视图
CREATE VIEW branded_food_details AS
SELECT 
    f.fdc_id,
    f.description,
    bf.brand_owner,
    bf.brand_name,
    bf.gtin_upc,
    bf.ingredients,
    bf.serving_size,
    bf.serving_size_unit,
    bf.branded_food_category
FROM foods f
JOIN branded_food bf ON f.fdc_id = bf.fdc_id
WHERE f.data_type = 'branded_food';

-- 插入一些基础数据
INSERT INTO measure_unit (id, name) VALUES 
(1, 'g'), (2, 'mg'), (3, 'µg'), (4, 'ml'), (5, 'cup'), 
(6, 'tbsp'), (7, 'tsp'), (8, 'oz'), (9, 'lb'), (10, 'piece');

-- 创建索引以优化查询性能
CREATE INDEX idx_foods_description_fulltext ON foods(description);
CREATE INDEX idx_branded_food_ingredients_fulltext ON branded_food(ingredients);
CREATE INDEX idx_food_nutrient_composite ON food_nutrient(fdc_id, nutrient_id, amount);
