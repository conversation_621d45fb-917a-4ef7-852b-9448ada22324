package com.example.meals.controller;

import com.example.meals.common.Result;
import com.example.meals.dto.FoodResponse;
import com.example.meals.dto.PageResponse;
import com.example.meals.dto.request.FoodRequest;
import com.example.meals.dto.NutritionAnalysisResponse;
import com.example.meals.service.FoodService;
import com.example.meals.utils.AuthUtil;
import jakarta.servlet.http.HttpServletRequest;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 食物控制器
 */
@RestController
@RequestMapping("/api/foods")
public class FoodController {
    
    @Autowired
    private FoodService foodService;
    
    /**
     * 获取启用的食物列表（公开接口）
     */
    @GetMapping("/enabled")
    public Result<List<FoodResponse>> getEnabledFoods() {
        return foodService.getEnabledFoods();
    }
    
    /**
     * 搜索食物（公开接口）
     */
    @GetMapping("/search")
    public Result<List<FoodResponse>> searchFoods(
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category) {
        return foodService.searchFoods(keyword, category);
    }
    
    /**
     * 根据ID获取食物详情（公开接口）
     */
    @GetMapping("/{id}")
    public Result<FoodResponse> getFoodById(@PathVariable Long id) {
        return foodService.getFoodById(id);
    }
    
    /**
     * 获取所有食物分类（公开接口）
     */
    @GetMapping("/categories")
    public Result<List<String>> getAllCategories() {
        return foodService.getAllCategories();
    }
    
    /**
     * 营养分析计算（公开接口）
     */
    @PostMapping("/{id}/analyze")
    public Result<NutritionAnalysisResponse> analyzeNutrition(
            @PathVariable("id") Long id,
            @RequestParam("weight") Double weight) {

        System.out.println("营养分析API被调用: id=" + id + ", weight=" + weight);
        try {
            Result<NutritionAnalysisResponse> result = foodService.calculateNutrition(id, weight);
            System.out.println("营养分析结果: " + result.getCode());
            return result;
        } catch (Exception e) {
            System.out.println("营养分析异常: " + e.getMessage());
            e.printStackTrace();
            return Result.error("营养分析计算失败");
        }
    }
    
    /**
     * 获取热门食物（公开接口）
     */
    @GetMapping("/popular")
    public Result<List<FoodResponse>> getPopularFoods(
            @RequestParam(defaultValue = "10") Integer limit) {
        return foodService.getPopularFoods(limit);
    }
    
    /**
     * 获取推荐食物（公开接口）
     */
    @GetMapping("/recommended")
    public Result<List<FoodResponse>> getRecommendedFoods(
            @RequestParam(defaultValue = "10") Integer limit) {
        return foodService.getRecommendedFoods(limit);
    }
    
    /**
     * 根据营养成分范围查询食物（公开接口）
     */
    @GetMapping("/nutrition-range")
    public Result<List<FoodResponse>> getFoodsByNutritionRange(
            @RequestParam(required = false) Double minCalories,
            @RequestParam(required = false) Double maxCalories,
            @RequestParam(required = false) Double minProtein,
            @RequestParam(required = false) Double maxProtein,
            @RequestParam(required = false) Double minFat,
            @RequestParam(required = false) Double maxFat,
            @RequestParam(required = false) Double minCarbs,
            @RequestParam(required = false) Double maxCarbs) {
        
        return foodService.getFoodsByNutritionRange(
                minCalories, maxCalories,
                minProtein, maxProtein,
                minFat, maxFat,
                minCarbs, maxCarbs
        );
    }
    
    // ========== 管理员功能 ==========
    
    /**
     * 获取所有食物（管理员功能）
     */
    @GetMapping("/admin/all")
    public Result<List<FoodResponse>> getAllFoods(HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.getAllFoods();
    }
    
    /**
     * 分页查询食物（管理员功能）
     */
    @GetMapping("/admin/page")
    public Result<PageResponse<FoodResponse>> getFoodsByPage(
            @RequestParam(defaultValue = "1") Integer page,
            @RequestParam(defaultValue = "10") Integer size,
            @RequestParam(required = false) String keyword,
            @RequestParam(required = false) String category,
            @RequestParam(required = false) Integer status,
            HttpServletRequest request) {
        
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.getFoodsByPage(page, size, keyword, category, status);
    }
    
    /**
     * 创建食物（管理员功能）
     */
    @PostMapping("/admin/create")
    public Result<FoodResponse> createFood(@RequestBody FoodRequest request, HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.createFood(request);
    }
    
    /**
     * 更新食物（管理员功能）
     */
    @PutMapping("/admin/{id}")
    public Result<FoodResponse> updateFood(@PathVariable Long id, @RequestBody FoodRequest request, HttpServletRequest httpRequest) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(httpRequest);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.updateFood(id, request);
    }
    
    /**
     * 删除食物（管理员功能）
     */
    @DeleteMapping("/admin/{id}")
    public Result<Void> deleteFood(@PathVariable Long id, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.deleteFood(id);
    }
    
    /**
     * 检查食物名称是否可用（管理员功能）
     */
    @GetMapping("/admin/check-name")
    public Result<Boolean> checkFoodName(
            @RequestParam String name,
            @RequestParam(required = false) Long excludeId,
            HttpServletRequest request) {
        
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.checkFoodName(name, excludeId);
    }
    
    /**
     * 批量导入食物（管理员功能）
     */
    @PostMapping("/admin/batch-import")
    public Result<Integer> batchImportFoods(@RequestBody List<FoodRequest> requests, HttpServletRequest request) {
        // 验证管理员权限
        Result<Void> authResult = AuthUtil.checkAdminPermission(request);
        if (authResult != null) {
            return Result.forbidden(authResult.getMessage());
        }
        
        return foodService.batchImportFoods(requests);
    }
}
