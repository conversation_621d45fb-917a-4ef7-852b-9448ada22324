#!/usr/bin/env python3
"""
USDA FoodData Central CSV数据导入MySQL脚本
将下载的CSV文件导入到MySQL数据库中
"""

import os
import pandas as pd
import mysql.connector
from mysql.connector import Error
import logging
from pathlib import Path
import sys
from datetime import datetime

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class MySQLImporter:
    def __init__(self, host='localhost', database='meals', user='root', password=''):
        self.host = host
        self.database = database
        self.user = user
        self.password = password
        self.connection = None

        # CSV文件映射到数据库表 (使用usda_前缀)
        self.csv_table_mapping = {
            'food.csv': 'usda_foods',
            'food_category.csv': 'usda_food_category',
            'nutrient.csv': 'usda_nutrient',
            'food_nutrient.csv': 'usda_food_nutrient',
            'food_attribute.csv': 'usda_food_attribute',
            'food_attribute_type.csv': 'usda_food_attribute_type',
            'branded_food.csv': 'usda_branded_food',
            'foundation_food.csv': 'usda_foundation_food',
            'sr_legacy_food.csv': 'usda_sr_legacy_food',
            'survey_fndds_food.csv': 'usda_survey_fndds_food',
            'measure_unit.csv': 'usda_measure_unit',
            'food_portion.csv': 'usda_food_portion',
            'nutrient_incoming_name.csv': 'usda_nutrient_incoming_name',
            'food_update_log_entry.csv': 'usda_food_update_log_entry'
        }
        
        # 表导入顺序 (考虑外键依赖，使用usda_前缀)
        self.import_order = [
            'usda_food_category',
            'usda_nutrient',
            'usda_measure_unit',
            'usda_food_attribute_type',
            'usda_foods',
            'usda_food_attribute',
            'usda_food_nutrient',
            'usda_branded_food',
            'usda_foundation_food',
            'usda_sr_legacy_food',
            'usda_survey_fndds_food',
            'usda_food_portion',
            'usda_nutrient_incoming_name',
            'usda_food_update_log_entry'
        ]

    def connect(self):
        """连接到MySQL数据库"""
        try:
            self.connection = mysql.connector.connect(
                host=self.host,
                database=self.database,
                user=self.user,
                password=self.password,
                charset='utf8mb4',
                use_unicode=True,
                autocommit=False
            )
            
            if self.connection.is_connected():
                logger.info(f"成功连接到MySQL数据库: {self.database}")
                return True
                
        except Error as e:
            logger.error(f"连接MySQL失败: {e}")
            return False

    def disconnect(self):
        """断开数据库连接"""
        if self.connection and self.connection.is_connected():
            self.connection.close()
            logger.info("MySQL连接已关闭")

    def execute_sql_file(self, sql_file_path):
        """执行SQL文件"""
        try:
            with open(sql_file_path, 'r', encoding='utf-8') as file:
                sql_script = file.read()
            
            # 分割SQL语句
            sql_commands = sql_script.split(';')
            cursor = self.connection.cursor()
            
            for command in sql_commands:
                command = command.strip()
                if command:
                    try:
                        cursor.execute(command)
                    except Error as e:
                        logger.warning(f"执行SQL命令时出现警告: {e}")
                        logger.warning(f"命令: {command[:100]}...")
            
            self.connection.commit()
            cursor.close()
            logger.info(f"SQL文件执行完成: {sql_file_path}")
            return True
            
        except Exception as e:
            logger.error(f"执行SQL文件失败: {e}")
            return False

    def clean_dataframe(self, df, table_name):
        """清理DataFrame数据"""
        # 处理空值
        df = df.where(pd.notnull(df), None)
        
        # 处理特定表的数据清理
        if table_name == 'usda_foods':
            # 确保fdc_id是整数
            df['fdc_id'] = pd.to_numeric(df['fdc_id'], errors='coerce')
            df = df.dropna(subset=['fdc_id'])
            df['fdc_id'] = df['fdc_id'].astype(int)

            # 处理日期字段
            if 'publication_date' in df.columns:
                df['publication_date'] = pd.to_datetime(df['publication_date'], errors='coerce')

        elif table_name == 'usda_food_nutrient':
            # 确保数值字段正确
            numeric_columns = ['fdc_id', 'nutrient_id', 'amount', 'data_points', 'min_value', 'max_value', 'median_value']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

        elif table_name == 'usda_branded_food':
            # 处理日期字段
            date_columns = ['modified_date', 'available_date', 'discontinued_date']
            for col in date_columns:
                if col in df.columns:
                    df[col] = pd.to_datetime(df[col], errors='coerce')
        
        return df

    def import_csv_to_table(self, csv_file_path, table_name, batch_size=1000):
        """将CSV文件导入到指定表"""
        try:
            logger.info(f"开始导入 {csv_file_path} 到表 {table_name}")
            
            # 读取CSV文件
            df = pd.read_csv(csv_file_path, low_memory=False)
            logger.info(f"CSV文件包含 {len(df)} 行数据")
            
            # 清理数据
            df = self.clean_dataframe(df, table_name)
            
            # 获取表结构
            cursor = self.connection.cursor()
            cursor.execute(f"DESCRIBE {table_name}")
            table_columns = [column[0] for column in cursor.fetchall()]
            
            # 只保留表中存在的列
            df_columns = [col for col in df.columns if col in table_columns]
            df = df[df_columns]
            
            logger.info(f"匹配的列: {df_columns}")
            
            # 分批导入数据
            total_rows = len(df)
            imported_rows = 0
            
            for start_idx in range(0, total_rows, batch_size):
                end_idx = min(start_idx + batch_size, total_rows)
                batch_df = df.iloc[start_idx:end_idx]
                
                # 构建INSERT语句
                placeholders = ', '.join(['%s'] * len(df_columns))
                columns_str = ', '.join(df_columns)
                insert_query = f"INSERT IGNORE INTO {table_name} ({columns_str}) VALUES ({placeholders})"
                
                # 准备数据
                data_tuples = [tuple(row) for row in batch_df.values]
                
                try:
                    cursor.executemany(insert_query, data_tuples)
                    self.connection.commit()
                    imported_rows += len(batch_df)
                    
                    progress = (imported_rows / total_rows) * 100
                    print(f"\r导入进度: {progress:.1f}% ({imported_rows}/{total_rows})", end='')
                    
                except Error as e:
                    logger.error(f"批量导入失败: {e}")
                    self.connection.rollback()
                    break
            
            print()  # 换行
            cursor.close()
            logger.info(f"表 {table_name} 导入完成，共导入 {imported_rows} 行")
            return True
            
        except Exception as e:
            logger.error(f"导入CSV文件失败: {e}")
            return False

    def import_all_csv_files(self, csv_directory):
        """导入目录中的所有CSV文件"""
        csv_dir = Path(csv_directory)
        
        if not csv_dir.exists():
            logger.error(f"CSV目录不存在: {csv_directory}")
            return False
        
        # 按照依赖顺序导入
        for table_name in self.import_order:
            # 查找对应的CSV文件
            csv_file = None
            for csv_filename, mapped_table in self.csv_table_mapping.items():
                if mapped_table == table_name:
                    csv_path = csv_dir / csv_filename
                    if csv_path.exists():
                        csv_file = csv_path
                        break
            
            if csv_file:
                success = self.import_csv_to_table(csv_file, table_name)
                if not success:
                    logger.error(f"导入表 {table_name} 失败，停止导入")
                    return False
            else:
                logger.warning(f"未找到表 {table_name} 对应的CSV文件")
        
        logger.info("所有CSV文件导入完成")
        return True

    def get_import_statistics(self):
        """获取导入统计信息"""
        try:
            cursor = self.connection.cursor()
            stats = {}
            
            for table_name in self.import_order:
                cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
                count = cursor.fetchone()[0]
                stats[table_name] = count
            
            cursor.close()
            return stats
            
        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return {}

def main():
    """主函数"""
    print("USDA FoodData Central MySQL导入工具")
    print("=" * 50)
    
    # 数据库连接配置
    db_config = {
        'host': input("MySQL主机 (默认: localhost): ").strip() or 'localhost',
        'user': input("MySQL用户名 (默认: root): ").strip() or 'root',
        'password': input("MySQL密码: ").strip(),
        'database': input("数据库名 (默认: meals): ").strip() or 'meals'
    }
    
    # CSV文件目录
    csv_directory = input("CSV文件目录路径: ").strip()
    if not csv_directory:
        csv_directory = "./data/extracted"
    
    # 创建导入器
    importer = MySQLImporter(**db_config)
    
    # 连接数据库
    if not importer.connect():
        return
    
    try:
        # 执行数据库架构
        schema_file = Path("mysql_schema.sql")
        if schema_file.exists():
            print("正在创建数据库架构...")
            importer.execute_sql_file(schema_file)
        
        # 导入CSV数据
        print("开始导入CSV数据...")
        success = importer.import_all_csv_files(csv_directory)
        
        if success:
            # 显示统计信息
            print("\n导入统计:")
            stats = importer.get_import_statistics()
            for table, count in stats.items():
                print(f"  {table}: {count:,} 行")
            
            print("\n数据导入完成！")
        else:
            print("数据导入失败")
    
    finally:
        importer.disconnect()

if __name__ == "__main__":
    main()
