package com.example.meals.entity;

import java.time.LocalDateTime;

/**
 * 食物实体类
 */
public class Food {
    
    private Long id;
    private String name;                    // 食物名称
    private String category;                // 食物分类
    private String description;             // 食物描述
    private Integer status;                 // 状态：0-禁用，1-启用
    
    // 营养成分（每100g）
    private Double calories;                // 热量(千卡)
    private Double protein;                 // 蛋白质(g)
    private Double fat;                     // 脂肪(g)
    private Double carbohydrates;           // 碳水化合物(g)
    private Double fiber;                   // 膳食纤维(g)
    private Double sugar;                   // 糖分(g)
    private Double sodium;                  // 钠(mg)
    private Double calcium;                 // 钙(mg)
    private Double iron;                    // 铁(mg)
    private Double vitaminC;                // 维生素C(mg)
    private Double vitaminA;                // 维生素A(μg)
    private Double vitaminD;                // 维生素D(μg)
    private Double vitaminE;                // 维生素E(mg)
    private Double vitaminB1;               // 维生素B1(mg)
    private Double vitaminB2;               // 维生素B2(mg)
    private Double vitaminB6;               // 维生素B6(mg)
    private Double vitaminB12;              // 维生素B12(μg)
    private Double folicAcid;               // 叶酸(μg)
    private Double potassium;               // 钾(mg)
    private Double magnesium;               // 镁(mg)
    private Double zinc;                    // 锌(mg)
    private Double phosphorus;              // 磷(mg)
    
    private LocalDateTime createTime;
    private LocalDateTime updateTime;
    
    // 构造函数
    public Food() {}
    
    public Food(String name, String category, String description) {
        this.name = name;
        this.category = category;
        this.description = description;
        this.status = 1; // 默认启用
        this.createTime = LocalDateTime.now();
        this.updateTime = LocalDateTime.now();
    }
    
    // Getter 和 Setter 方法
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getCategory() {
        return category;
    }
    
    public void setCategory(String category) {
        this.category = category;
    }
    
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Double getCalories() {
        return calories;
    }
    
    public void setCalories(Double calories) {
        this.calories = calories;
    }
    
    public Double getProtein() {
        return protein;
    }
    
    public void setProtein(Double protein) {
        this.protein = protein;
    }
    
    public Double getFat() {
        return fat;
    }
    
    public void setFat(Double fat) {
        this.fat = fat;
    }
    
    public Double getCarbohydrates() {
        return carbohydrates;
    }
    
    public void setCarbohydrates(Double carbohydrates) {
        this.carbohydrates = carbohydrates;
    }
    
    public Double getFiber() {
        return fiber;
    }
    
    public void setFiber(Double fiber) {
        this.fiber = fiber;
    }
    
    public Double getSugar() {
        return sugar;
    }
    
    public void setSugar(Double sugar) {
        this.sugar = sugar;
    }
    
    public Double getSodium() {
        return sodium;
    }
    
    public void setSodium(Double sodium) {
        this.sodium = sodium;
    }
    
    public Double getCalcium() {
        return calcium;
    }
    
    public void setCalcium(Double calcium) {
        this.calcium = calcium;
    }
    
    public Double getIron() {
        return iron;
    }
    
    public void setIron(Double iron) {
        this.iron = iron;
    }
    
    public Double getVitaminC() {
        return vitaminC;
    }
    
    public void setVitaminC(Double vitaminC) {
        this.vitaminC = vitaminC;
    }
    
    public Double getVitaminA() {
        return vitaminA;
    }
    
    public void setVitaminA(Double vitaminA) {
        this.vitaminA = vitaminA;
    }
    
    public Double getVitaminD() {
        return vitaminD;
    }
    
    public void setVitaminD(Double vitaminD) {
        this.vitaminD = vitaminD;
    }
    
    public Double getVitaminE() {
        return vitaminE;
    }
    
    public void setVitaminE(Double vitaminE) {
        this.vitaminE = vitaminE;
    }
    
    public Double getVitaminB1() {
        return vitaminB1;
    }
    
    public void setVitaminB1(Double vitaminB1) {
        this.vitaminB1 = vitaminB1;
    }
    
    public Double getVitaminB2() {
        return vitaminB2;
    }
    
    public void setVitaminB2(Double vitaminB2) {
        this.vitaminB2 = vitaminB2;
    }
    
    public Double getVitaminB6() {
        return vitaminB6;
    }
    
    public void setVitaminB6(Double vitaminB6) {
        this.vitaminB6 = vitaminB6;
    }
    
    public Double getVitaminB12() {
        return vitaminB12;
    }
    
    public void setVitaminB12(Double vitaminB12) {
        this.vitaminB12 = vitaminB12;
    }
    
    public Double getFolicAcid() {
        return folicAcid;
    }
    
    public void setFolicAcid(Double folicAcid) {
        this.folicAcid = folicAcid;
    }
    
    public Double getPotassium() {
        return potassium;
    }
    
    public void setPotassium(Double potassium) {
        this.potassium = potassium;
    }
    
    public Double getMagnesium() {
        return magnesium;
    }
    
    public void setMagnesium(Double magnesium) {
        this.magnesium = magnesium;
    }
    
    public Double getZinc() {
        return zinc;
    }
    
    public void setZinc(Double zinc) {
        this.zinc = zinc;
    }
    
    public Double getPhosphorus() {
        return phosphorus;
    }
    
    public void setPhosphorus(Double phosphorus) {
        this.phosphorus = phosphorus;
    }
    
    public LocalDateTime getCreateTime() {
        return createTime;
    }
    
    public void setCreateTime(LocalDateTime createTime) {
        this.createTime = createTime;
    }
    
    public LocalDateTime getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(LocalDateTime updateTime) {
        this.updateTime = updateTime;
    }
    
    @Override
    public String toString() {
        return "Food{" +
                "id=" + id +
                ", name='" + name + '\'' +
                ", category='" + category + '\'' +
                ", calories=" + calories +
                ", protein=" + protein +
                ", fat=" + fat +
                ", carbohydrates=" + carbohydrates +
                ", status=" + status +
                ", createTime=" + createTime +
                '}';
    }
}
