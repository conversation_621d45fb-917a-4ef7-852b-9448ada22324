# USDA FoodData Central MySQL数据库导入工具

这个工具包帮助您下载USDA FoodData Central的完整食物营养数据库，并将其集成到现有的`meals`数据库中。

## 📊 数据库内容

- **35万+食物条目**：包含完整的营养成分信息
- **权威数据源**：美国农业部官方数据
- **多种食物类型**：
  - Foundation Foods (基础食物)
  - SR Legacy (标准参考食物)
  - FNDDS (膳食研究食物)
  - Branded Foods (品牌食品)

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 下载数据

```bash
python download_usda_data.py
```

选择下载选项：
- **选项1**: 完整数据集 (~3GB) - 推荐
- **选项2-5**: 特定数据类型

### 3. 确保meals数据库存在

数据将导入到现有的`meals`数据库中，确保数据库已创建：

```sql
-- 如果meals数据库不存在，请先创建
CREATE DATABASE IF NOT EXISTS meals CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
```

### 4. 导入数据到MySQL

```bash
python mysql_import.py
```

按提示输入：
- MySQL连接信息
- CSV文件目录路径

## 📋 数据库架构

### 核心表结构 (集成到meals数据库)

- **usda_foods**: USDA食物主表 (35万+条目)
- **usda_food_nutrient**: 营养素含量 (核心数据)
- **usda_nutrient**: 营养素定义
- **usda_food_category**: 食物分类
- **usda_branded_food**: 品牌食品扩展信息
- **user_favorite_usda_foods**: 用户收藏USDA食物关联表
- **user_food_usda_mapping**: 用户食物与USDA食物映射表

### 常用查询示例

```sql
-- 查询苹果的营养信息
SELECT f.description, n.name, fn.amount, n.unit_name
FROM foods f
JOIN food_nutrient fn ON f.fdc_id = fn.fdc_id
JOIN nutrient n ON fn.nutrient_id = n.id
WHERE f.description LIKE '%apple%'
AND n.name IN ('Energy', 'Protein', 'Carbohydrate', 'Total lipid (fat)');

-- 查询高蛋白食物 (蛋白质含量 > 20g/100g)
SELECT f.description, fn.amount as protein_content
FROM foods f
JOIN food_nutrient fn ON f.fdc_id = fn.fdc_id
JOIN nutrient n ON fn.nutrient_id = n.id
WHERE n.name = 'Protein' AND fn.amount > 20
ORDER BY fn.amount DESC;

-- 查询特定品牌的产品
SELECT f.description, bf.brand_owner, bf.serving_size, bf.serving_size_unit
FROM foods f
JOIN branded_food bf ON f.fdc_id = bf.fdc_id
WHERE bf.brand_owner LIKE '%Kellogg%';
```

## 📁 文件说明

- `download_usda_data.py`: 数据下载脚本
- `mysql_schema.sql`: MySQL数据库架构
- `mysql_import.py`: 数据导入脚本
- `requirements.txt`: Python依赖包

## ⚙️ 配置选项

### 下载配置
- 支持选择性下载特定数据类型
- 自动解压和文件管理
- 下载进度显示

### 导入配置
- 批量导入 (默认1000行/批)
- 数据清理和验证
- 外键依赖处理
- 错误恢复机制

## 📈 性能优化

- 创建了关键字段索引
- 使用批量导入提高速度
- 支持大数据集处理
- 内存优化的数据处理

## 🔍 数据质量

- 自动数据类型转换
- 空值处理
- 重复数据去除
- 数据完整性验证

## 💡 使用建议

1. **首次使用**：建议下载完整数据集
2. **生产环境**：配置适当的MySQL参数
3. **查询优化**：利用已创建的索引
4. **定期更新**：USDA每年更新2次数据

## 🆘 常见问题

### Q: 导入时间很长怎么办？
A: 完整数据集导入需要30-60分钟，这是正常的。可以先导入Foundation Foods测试。

### Q: 内存不足怎么办？
A: 调整batch_size参数，减少每批处理的数据量。

### Q: 如何更新数据？
A: 重新下载最新数据并重新导入，或使用增量更新脚本。

## 📞 技术支持

如有问题，请检查：
1. MySQL连接配置
2. 文件路径是否正确
3. 磁盘空间是否充足
4. Python依赖是否完整安装
