(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const r of document.querySelectorAll('link[rel="modulepreload"]'))o(r);new MutationObserver(r=>{for(const i of r)if(i.type==="childList")for(const l of i.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function s(r){const i={};return r.integrity&&(i.integrity=r.integrity),r.referrerPolicy&&(i.referrerPolicy=r.referrerPolicy),r.crossOrigin==="use-credentials"?i.credentials="include":r.crossOrigin==="anonymous"?i.credentials="omit":i.credentials="same-origin",i}function o(r){if(r.ep)return;r.ep=!0;const i=s(r);fetch(r.href,i)}})();/**
* @vue/shared v3.5.18
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function vo(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const Se={},hs=[],Ct=()=>{},rl=()=>!1,Sn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),go=e=>e.startsWith("onUpdate:"),qe=Object.assign,mo=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},il=Object.prototype.hasOwnProperty,xe=(e,t)=>il.call(e,t),ne=Array.isArray,vs=e=>Qs(e)==="[object Map]",$s=e=>Qs(e)==="[object Set]",No=e=>Qs(e)==="[object Date]",ce=e=>typeof e=="function",Re=e=>typeof e=="string",mt=e=>typeof e=="symbol",Pe=e=>e!==null&&typeof e=="object",Nr=e=>(Pe(e)||ce(e))&&ce(e.then)&&ce(e.catch),Dr=Object.prototype.toString,Qs=e=>Dr.call(e),ll=e=>Qs(e).slice(8,-1),Br=e=>Qs(e)==="[object Object]",wo=e=>Re(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,Os=vo(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Tn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},al=/-(\w)/g,dt=Tn(e=>e.replace(al,(t,s)=>s?s.toUpperCase():"")),cl=/\B([A-Z])/g,Jt=Tn(e=>e.replace(cl,"-$1").toLowerCase()),En=Tn(e=>e.charAt(0).toUpperCase()+e.slice(1)),Dn=Tn(e=>e?`on${En(e)}`:""),Wt=(e,t)=>!Object.is(e,t),un=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Qn=(e,t,s,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:s})},mn=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Do;const Pn=()=>Do||(Do=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Ot(e){if(ne(e)){const t={};for(let s=0;s<e.length;s++){const o=e[s],r=Re(o)?pl(o):Ot(o);if(r)for(const i in r)t[i]=r[i]}return t}else if(Re(e)||Pe(e))return e}const ul=/;(?![^(]*\))/g,dl=/:([^]+)/,fl=/\/\*[^]*?\*\//g;function pl(e){const t={};return e.replace(fl,"").split(ul).forEach(s=>{if(s){const o=s.split(dl);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function pe(e){let t="";if(Re(e))t=e;else if(ne(e))for(let s=0;s<e.length;s++){const o=pe(e[s]);o&&(t+=o+" ")}else if(Pe(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const hl="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",vl=vo(hl);function Fr(e){return!!e||e===""}function gl(e,t){if(e.length!==t.length)return!1;let s=!0;for(let o=0;s&&o<e.length;o++)s=ls(e[o],t[o]);return s}function ls(e,t){if(e===t)return!0;let s=No(e),o=No(t);if(s||o)return s&&o?e.getTime()===t.getTime():!1;if(s=mt(e),o=mt(t),s||o)return e===t;if(s=ne(e),o=ne(t),s||o)return s&&o?gl(e,t):!1;if(s=Pe(e),o=Pe(t),s||o){if(!s||!o)return!1;const r=Object.keys(e).length,i=Object.keys(t).length;if(r!==i)return!1;for(const l in e){const c=e.hasOwnProperty(l),a=t.hasOwnProperty(l);if(c&&!a||!c&&a||!ls(e[l],t[l]))return!1}}return String(e)===String(t)}function yo(e,t){return e.findIndex(s=>ls(s,t))}const Hr=e=>!!(e&&e.__v_isRef===!0),S=e=>Re(e)?e:e==null?"":ne(e)||Pe(e)&&(e.toString===Dr||!ce(e.toString))?Hr(e)?S(e.value):JSON.stringify(e,zr,2):String(e),zr=(e,t)=>Hr(t)?zr(e,t.value):vs(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[o,r],i)=>(s[Bn(o,i)+" =>"]=r,s),{})}:$s(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Bn(s))}:mt(t)?Bn(t):Pe(t)&&!ne(t)&&!Br(t)?String(t):t,Bn=(e,t="")=>{var s;return mt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let st;class Kr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=st,!t&&st&&(this.index=(st.scopes||(st.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=st;try{return st=this,t()}finally{st=s}}}on(){++this._on===1&&(this.prevScope=st,st=this)}off(){this._on>0&&--this._on===0&&(st=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,o;for(s=0,o=this.effects.length;s<o;s++)this.effects[s].stop();for(this.effects.length=0,s=0,o=this.cleanups.length;s<o;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,o=this.scopes.length;s<o;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const r=this.parent.scopes.pop();r&&r!==this&&(this.parent.scopes[this.index]=r,r.index=this.index)}this.parent=void 0}}}function ml(e){return new Kr(e)}function wl(){return st}let Ee;const Fn=new WeakSet;class Gr{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,st&&st.active&&st.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Fn.has(this)&&(Fn.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||qr(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Bo(this),Zr(this);const t=Ee,s=vt;Ee=this,vt=!0;try{return this.fn()}finally{Jr(this),Ee=t,vt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)ko(t);this.deps=this.depsTail=void 0,Bo(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Fn.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Xn(this)&&this.run()}get dirty(){return Xn(this)}}let Wr=0,Rs,Is;function qr(e,t=!1){if(e.flags|=8,t){e.next=Is,Is=e;return}e.next=Rs,Rs=e}function bo(){Wr++}function _o(){if(--Wr>0)return;if(Is){let t=Is;for(Is=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;Rs;){let t=Rs;for(Rs=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=s}}if(e)throw e}function Zr(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Jr(e){let t,s=e.depsTail,o=s;for(;o;){const r=o.prevDep;o.version===-1?(o===s&&(s=r),ko(o),yl(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=r}e.deps=t,e.depsTail=s}function Xn(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(Yr(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function Yr(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Hs)||(e.globalVersion=Hs,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Xn(e))))return;e.flags|=2;const t=e.dep,s=Ee,o=vt;Ee=e,vt=!0;try{Zr(e);const r=e.fn(e._value);(t.version===0||Wt(r,e._value))&&(e.flags|=128,e._value=r,t.version++)}catch(r){throw t.version++,r}finally{Ee=s,vt=o,Jr(e),e.flags&=-3}}function ko(e,t=!1){const{dep:s,prevSub:o,nextSub:r}=e;if(o&&(o.nextSub=r,e.prevSub=void 0),r&&(r.prevSub=o,e.nextSub=void 0),s.subs===e&&(s.subs=o,!o&&s.computed)){s.computed.flags&=-5;for(let i=s.computed.deps;i;i=i.nextDep)ko(i,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function yl(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let vt=!0;const Qr=[];function Rt(){Qr.push(vt),vt=!1}function It(){const e=Qr.pop();vt=e===void 0?!0:e}function Bo(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=Ee;Ee=void 0;try{t()}finally{Ee=s}}}let Hs=0;class bl{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Co{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0,this.__v_skip=!0}track(t){if(!Ee||!vt||Ee===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==Ee)s=this.activeLink=new bl(Ee,this),Ee.deps?(s.prevDep=Ee.depsTail,Ee.depsTail.nextDep=s,Ee.depsTail=s):Ee.deps=Ee.depsTail=s,Xr(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const o=s.nextDep;o.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=o),s.prevDep=Ee.depsTail,s.nextDep=void 0,Ee.depsTail.nextDep=s,Ee.depsTail=s,Ee.deps===s&&(Ee.deps=o)}return s}trigger(t){this.version++,Hs++,this.notify(t)}notify(t){bo();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{_o()}}}function Xr(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)Xr(o)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const eo=new WeakMap,rs=Symbol(""),to=Symbol(""),zs=Symbol("");function Ke(e,t,s){if(vt&&Ee){let o=eo.get(e);o||eo.set(e,o=new Map);let r=o.get(s);r||(o.set(s,r=new Co),r.map=o,r.key=s),r.track()}}function At(e,t,s,o,r,i){const l=eo.get(e);if(!l){Hs++;return}const c=a=>{a&&a.trigger()};if(bo(),t==="clear")l.forEach(c);else{const a=ne(e),u=a&&wo(s);if(a&&s==="length"){const d=Number(o);l.forEach((f,v)=>{(v==="length"||v===zs||!mt(v)&&v>=d)&&c(f)})}else switch((s!==void 0||l.has(void 0))&&c(l.get(s)),u&&c(l.get(zs)),t){case"add":a?u&&c(l.get("length")):(c(l.get(rs)),vs(e)&&c(l.get(to)));break;case"delete":a||(c(l.get(rs)),vs(e)&&c(l.get(to)));break;case"set":vs(e)&&c(l.get(rs));break}}_o()}function us(e){const t=ke(e);return t===e?t:(Ke(t,"iterate",zs),ct(e)?t:t.map(Be))}function An(e){return Ke(e=ke(e),"iterate",zs),e}const _l={__proto__:null,[Symbol.iterator](){return Hn(this,Symbol.iterator,Be)},concat(...e){return us(this).concat(...e.map(t=>ne(t)?us(t):t))},entries(){return Hn(this,"entries",e=>(e[1]=Be(e[1]),e))},every(e,t){return Tt(this,"every",e,t,void 0,arguments)},filter(e,t){return Tt(this,"filter",e,t,s=>s.map(Be),arguments)},find(e,t){return Tt(this,"find",e,t,Be,arguments)},findIndex(e,t){return Tt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Tt(this,"findLast",e,t,Be,arguments)},findLastIndex(e,t){return Tt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Tt(this,"forEach",e,t,void 0,arguments)},includes(...e){return zn(this,"includes",e)},indexOf(...e){return zn(this,"indexOf",e)},join(e){return us(this).join(e)},lastIndexOf(...e){return zn(this,"lastIndexOf",e)},map(e,t){return Tt(this,"map",e,t,void 0,arguments)},pop(){return Ps(this,"pop")},push(...e){return Ps(this,"push",e)},reduce(e,...t){return Fo(this,"reduce",e,t)},reduceRight(e,...t){return Fo(this,"reduceRight",e,t)},shift(){return Ps(this,"shift")},some(e,t){return Tt(this,"some",e,t,void 0,arguments)},splice(...e){return Ps(this,"splice",e)},toReversed(){return us(this).toReversed()},toSorted(e){return us(this).toSorted(e)},toSpliced(...e){return us(this).toSpliced(...e)},unshift(...e){return Ps(this,"unshift",e)},values(){return Hn(this,"values",Be)}};function Hn(e,t,s){const o=An(e),r=o[t]();return o!==e&&!ct(e)&&(r._next=r.next,r.next=()=>{const i=r._next();return i.value&&(i.value=s(i.value)),i}),r}const kl=Array.prototype;function Tt(e,t,s,o,r,i){const l=An(e),c=l!==e&&!ct(e),a=l[t];if(a!==kl[t]){const f=a.apply(e,i);return c?Be(f):f}let u=s;l!==e&&(c?u=function(f,v){return s.call(this,Be(f),v,e)}:s.length>2&&(u=function(f,v){return s.call(this,f,v,e)}));const d=a.call(l,u,o);return c&&r?r(d):d}function Fo(e,t,s,o){const r=An(e);let i=s;return r!==e&&(ct(e)?s.length>3&&(i=function(l,c,a){return s.call(this,l,c,a,e)}):i=function(l,c,a){return s.call(this,l,Be(c),a,e)}),r[t](i,...o)}function zn(e,t,s){const o=ke(e);Ke(o,"iterate",zs);const r=o[t](...s);return(r===-1||r===!1)&&So(s[0])?(s[0]=ke(s[0]),o[t](...s)):r}function Ps(e,t,s=[]){Rt(),bo();const o=ke(e)[t].apply(e,s);return _o(),It(),o}const Cl=vo("__proto__,__v_isRef,__isVue"),ei=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(mt));function xl(e){mt(e)||(e=String(e));const t=ke(this);return Ke(t,"has",e),t.hasOwnProperty(e)}class ti{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,o){if(s==="__v_skip")return t.__v_skip;const r=this._isReadonly,i=this._isShallow;if(s==="__v_isReactive")return!r;if(s==="__v_isReadonly")return r;if(s==="__v_isShallow")return i;if(s==="__v_raw")return o===(r?i?Rl:ri:i?oi:ni).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const l=ne(t);if(!r){let a;if(l&&(a=_l[s]))return a;if(s==="hasOwnProperty")return xl}const c=Reflect.get(t,s,We(t)?t:o);return(mt(s)?ei.has(s):Cl(s))||(r||Ke(t,"get",s),i)?c:We(c)?l&&wo(s)?c:c.value:Pe(c)?r?li(c):Me(c):c}}class si extends ti{constructor(t=!1){super(!1,t)}set(t,s,o,r){let i=t[s];if(!this._isShallow){const a=qt(i);if(!ct(o)&&!qt(o)&&(i=ke(i),o=ke(o)),!ne(t)&&We(i)&&!We(o))return a?!1:(i.value=o,!0)}const l=ne(t)&&wo(s)?Number(s)<t.length:xe(t,s),c=Reflect.set(t,s,o,We(t)?t:r);return t===ke(r)&&(l?Wt(o,i)&&At(t,"set",s,o):At(t,"add",s,o)),c}deleteProperty(t,s){const o=xe(t,s);t[s];const r=Reflect.deleteProperty(t,s);return r&&o&&At(t,"delete",s,void 0),r}has(t,s){const o=Reflect.has(t,s);return(!mt(s)||!ei.has(s))&&Ke(t,"has",s),o}ownKeys(t){return Ke(t,"iterate",ne(t)?"length":rs),Reflect.ownKeys(t)}}class $l extends ti{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Sl=new si,Tl=new $l,El=new si(!0);const so=e=>e,nn=e=>Reflect.getPrototypeOf(e);function Pl(e,t,s){return function(...o){const r=this.__v_raw,i=ke(r),l=vs(i),c=e==="entries"||e===Symbol.iterator&&l,a=e==="keys"&&l,u=r[e](...o),d=s?so:t?wn:Be;return!t&&Ke(i,"iterate",a?to:rs),{next(){const{value:f,done:v}=u.next();return v?{value:f,done:v}:{value:c?[d(f[0]),d(f[1])]:d(f),done:v}},[Symbol.iterator](){return this}}}}function on(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Al(e,t){const s={get(r){const i=this.__v_raw,l=ke(i),c=ke(r);e||(Wt(r,c)&&Ke(l,"get",r),Ke(l,"get",c));const{has:a}=nn(l),u=t?so:e?wn:Be;if(a.call(l,r))return u(i.get(r));if(a.call(l,c))return u(i.get(c));i!==l&&i.get(r)},get size(){const r=this.__v_raw;return!e&&Ke(ke(r),"iterate",rs),Reflect.get(r,"size",r)},has(r){const i=this.__v_raw,l=ke(i),c=ke(r);return e||(Wt(r,c)&&Ke(l,"has",r),Ke(l,"has",c)),r===c?i.has(r):i.has(r)||i.has(c)},forEach(r,i){const l=this,c=l.__v_raw,a=ke(c),u=t?so:e?wn:Be;return!e&&Ke(a,"iterate",rs),c.forEach((d,f)=>r.call(i,u(d),u(f),l))}};return qe(s,e?{add:on("add"),set:on("set"),delete:on("delete"),clear:on("clear")}:{add(r){!t&&!ct(r)&&!qt(r)&&(r=ke(r));const i=ke(this);return nn(i).has.call(i,r)||(i.add(r),At(i,"add",r,r)),this},set(r,i){!t&&!ct(i)&&!qt(i)&&(i=ke(i));const l=ke(this),{has:c,get:a}=nn(l);let u=c.call(l,r);u||(r=ke(r),u=c.call(l,r));const d=a.call(l,r);return l.set(r,i),u?Wt(i,d)&&At(l,"set",r,i):At(l,"add",r,i),this},delete(r){const i=ke(this),{has:l,get:c}=nn(i);let a=l.call(i,r);a||(r=ke(r),a=l.call(i,r)),c&&c.call(i,r);const u=i.delete(r);return a&&At(i,"delete",r,void 0),u},clear(){const r=ke(this),i=r.size!==0,l=r.clear();return i&&At(r,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(r=>{s[r]=Pl(r,e,t)}),s}function xo(e,t){const s=Al(e,t);return(o,r,i)=>r==="__v_isReactive"?!e:r==="__v_isReadonly"?e:r==="__v_raw"?o:Reflect.get(xe(s,r)&&r in o?s:o,r,i)}const Ml={get:xo(!1,!1)},Ll={get:xo(!1,!0)},Ol={get:xo(!0,!1)};const ni=new WeakMap,oi=new WeakMap,ri=new WeakMap,Rl=new WeakMap;function Il(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Ul(e){return e.__v_skip||!Object.isExtensible(e)?0:Il(ll(e))}function Me(e){return qt(e)?e:$o(e,!1,Sl,Ml,ni)}function ii(e){return $o(e,!1,El,Ll,oi)}function li(e){return $o(e,!0,Tl,Ol,ri)}function $o(e,t,s,o,r){if(!Pe(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const i=Ul(e);if(i===0)return e;const l=r.get(e);if(l)return l;const c=new Proxy(e,i===2?o:s);return r.set(e,c),c}function gs(e){return qt(e)?gs(e.__v_raw):!!(e&&e.__v_isReactive)}function qt(e){return!!(e&&e.__v_isReadonly)}function ct(e){return!!(e&&e.__v_isShallow)}function So(e){return e?!!e.__v_raw:!1}function ke(e){const t=e&&e.__v_raw;return t?ke(t):e}function ai(e){return!xe(e,"__v_skip")&&Object.isExtensible(e)&&Qn(e,"__v_skip",!0),e}const Be=e=>Pe(e)?Me(e):e,wn=e=>Pe(e)?li(e):e;function We(e){return e?e.__v_isRef===!0:!1}function j(e){return ci(e,!1)}function Vl(e){return ci(e,!0)}function ci(e,t){return We(e)?e:new jl(e,t)}class jl{constructor(t,s){this.dep=new Co,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:ke(t),this._value=s?t:Be(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,o=this.__v_isShallow||ct(t)||qt(t);t=o?t:ke(t),Wt(t,s)&&(this._rawValue=t,this._value=o?t:Be(t),this.dep.trigger())}}function fe(e){return We(e)?e.value:e}const Nl={get:(e,t,s)=>t==="__v_raw"?e:fe(Reflect.get(e,t,s)),set:(e,t,s,o)=>{const r=e[t];return We(r)&&!We(s)?(r.value=s,!0):Reflect.set(e,t,s,o)}};function ui(e){return gs(e)?e:new Proxy(e,Nl)}class Dl{constructor(t,s,o){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Co(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Hs-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&Ee!==this)return qr(this,!0),!0}get value(){const t=this.dep.track();return Yr(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function Bl(e,t,s=!1){let o,r;return ce(e)?o=e:(o=e.get,r=e.set),new Dl(o,r,s)}const rn={},yn=new WeakMap;let ss;function Fl(e,t=!1,s=ss){if(s){let o=yn.get(s);o||yn.set(s,o=[]),o.push(e)}}function Hl(e,t,s=Se){const{immediate:o,deep:r,once:i,scheduler:l,augmentJob:c,call:a}=s,u=B=>r?B:ct(B)||r===!1||r===0?Mt(B,1):Mt(B);let d,f,v,g,_=!1,y=!1;if(We(e)?(f=()=>e.value,_=ct(e)):gs(e)?(f=()=>u(e),_=!0):ne(e)?(y=!0,_=e.some(B=>gs(B)||ct(B)),f=()=>e.map(B=>{if(We(B))return B.value;if(gs(B))return u(B);if(ce(B))return a?a(B,2):B()})):ce(e)?t?f=a?()=>a(e,2):e:f=()=>{if(v){Rt();try{v()}finally{It()}}const B=ss;ss=d;try{return a?a(e,3,[g]):e(g)}finally{ss=B}}:f=Ct,t&&r){const B=f,Z=r===!0?1/0:r;f=()=>Mt(B(),Z)}const w=wl(),T=()=>{d.stop(),w&&w.active&&mo(w.effects,d)};if(i&&t){const B=t;t=(...Z)=>{B(...Z),T()}}let A=y?new Array(e.length).fill(rn):rn;const U=B=>{if(!(!(d.flags&1)||!d.dirty&&!B))if(t){const Z=d.run();if(r||_||(y?Z.some((N,oe)=>Wt(N,A[oe])):Wt(Z,A))){v&&v();const N=ss;ss=d;try{const oe=[Z,A===rn?void 0:y&&A[0]===rn?[]:A,g];A=Z,a?a(t,3,oe):t(...oe)}finally{ss=N}}}else d.run()};return c&&c(U),d=new Gr(f),d.scheduler=l?()=>l(U,!1):U,g=B=>Fl(B,!1,d),v=d.onStop=()=>{const B=yn.get(d);if(B){if(a)a(B,4);else for(const Z of B)Z();yn.delete(d)}},t?o?U(!0):A=d.run():l?l(U.bind(null,!0),!0):d.run(),T.pause=d.pause.bind(d),T.resume=d.resume.bind(d),T.stop=T,T}function Mt(e,t=1/0,s){if(t<=0||!Pe(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,We(e))Mt(e.value,t,s);else if(ne(e))for(let o=0;o<e.length;o++)Mt(e[o],t,s);else if($s(e)||vs(e))e.forEach(o=>{Mt(o,t,s)});else if(Br(e)){for(const o in e)Mt(e[o],t,s);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Mt(e[o],t,s)}return e}/**
* @vue/runtime-core v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Xs(e,t,s,o){try{return o?e(...o):e()}catch(r){Mn(r,t,s)}}function xt(e,t,s,o){if(ce(e)){const r=Xs(e,t,s,o);return r&&Nr(r)&&r.catch(i=>{Mn(i,t,s)}),r}if(ne(e)){const r=[];for(let i=0;i<e.length;i++)r.push(xt(e[i],t,s,o));return r}}function Mn(e,t,s,o=!0){const r=t?t.vnode:null,{errorHandler:i,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||Se;if(t){let c=t.parent;const a=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${s}`;for(;c;){const d=c.ec;if(d){for(let f=0;f<d.length;f++)if(d[f](e,a,u)===!1)return}c=c.parent}if(i){Rt(),Xs(i,null,10,[e,a,u]),It();return}}zl(e,s,r,o,l)}function zl(e,t,s,o=!0,r=!1){if(r)throw e;console.error(e)}const Qe=[];let bt=-1;const ms=[];let zt=null,fs=0;const di=Promise.resolve();let bn=null;function To(e){const t=bn||di;return e?t.then(this?e.bind(this):e):t}function Kl(e){let t=bt+1,s=Qe.length;for(;t<s;){const o=t+s>>>1,r=Qe[o],i=Ks(r);i<e||i===e&&r.flags&2?t=o+1:s=o}return t}function Eo(e){if(!(e.flags&1)){const t=Ks(e),s=Qe[Qe.length-1];!s||!(e.flags&2)&&t>=Ks(s)?Qe.push(e):Qe.splice(Kl(t),0,e),e.flags|=1,fi()}}function fi(){bn||(bn=di.then(hi))}function Gl(e){ne(e)?ms.push(...e):zt&&e.id===-1?zt.splice(fs+1,0,e):e.flags&1||(ms.push(e),e.flags|=1),fi()}function Ho(e,t,s=bt+1){for(;s<Qe.length;s++){const o=Qe[s];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Qe.splice(s,1),s--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function pi(e){if(ms.length){const t=[...new Set(ms)].sort((s,o)=>Ks(s)-Ks(o));if(ms.length=0,zt){zt.push(...t);return}for(zt=t,fs=0;fs<zt.length;fs++){const s=zt[fs];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}zt=null,fs=0}}const Ks=e=>e.id==null?e.flags&2?-1:1/0:e.id;function hi(e){try{for(bt=0;bt<Qe.length;bt++){const t=Qe[bt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Xs(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;bt<Qe.length;bt++){const t=Qe[bt];t&&(t.flags&=-2)}bt=-1,Qe.length=0,pi(),bn=null,(Qe.length||ms.length)&&hi()}}let Fe=null,vi=null;function _n(e){const t=Fe;return Fe=e,vi=e&&e.type.__scopeId||null,t}function me(e,t=Fe,s){if(!t||e._n)return e;const o=(...r)=>{o._d&&sr(-1);const i=_n(t);let l;try{l=e(...r)}finally{_n(i),o._d&&sr(1)}return l};return o._n=!0,o._c=!0,o._d=!0,o}function te(e,t){if(Fe===null)return e;const s=In(Fe),o=e.dirs||(e.dirs=[]);for(let r=0;r<t.length;r++){let[i,l,c,a=Se]=t[r];i&&(ce(i)&&(i={mounted:i,updated:i}),i.deep&&Mt(l),o.push({dir:i,instance:s,value:l,oldValue:void 0,arg:c,modifiers:a}))}return e}function Xt(e,t,s,o){const r=e.dirs,i=t&&t.dirs;for(let l=0;l<r.length;l++){const c=r[l];i&&(c.oldValue=i[l].value);let a=c.dir[o];a&&(Rt(),xt(a,s,8,[e.el,c,e,t]),It())}}const gi=Symbol("_vte"),Wl=e=>e.__isTeleport,Us=e=>e&&(e.disabled||e.disabled===""),zo=e=>e&&(e.defer||e.defer===""),Ko=e=>typeof SVGElement<"u"&&e instanceof SVGElement,Go=e=>typeof MathMLElement=="function"&&e instanceof MathMLElement,no=(e,t)=>{const s=e&&e.to;return Re(s)?t?t(s):null:s},mi={name:"Teleport",__isTeleport:!0,process(e,t,s,o,r,i,l,c,a,u){const{mc:d,pc:f,pbc:v,o:{insert:g,querySelector:_,createText:y,createComment:w}}=u,T=Us(t.props);let{shapeFlag:A,children:U,dynamicChildren:B}=t;if(e==null){const Z=t.el=y(""),N=t.anchor=y("");g(Z,s,o),g(N,s,o);const oe=(k,z)=>{A&16&&(r&&r.isCE&&(r.ce._teleportTarget=k),d(U,k,z,r,i,l,c,a))},I=()=>{const k=t.target=no(t.props,_),z=wi(k,t,y,g);k&&(l!=="svg"&&Ko(k)?l="svg":l!=="mathml"&&Go(k)&&(l="mathml"),T||(oe(k,z),dn(t,!1)))};T&&(oe(s,N),dn(t,!0)),zo(t.props)?(t.el.__isMounted=!1,Ye(()=>{I(),delete t.el.__isMounted},i)):I()}else{if(zo(t.props)&&e.el.__isMounted===!1){Ye(()=>{mi.process(e,t,s,o,r,i,l,c,a,u)},i);return}t.el=e.el,t.targetStart=e.targetStart;const Z=t.anchor=e.anchor,N=t.target=e.target,oe=t.targetAnchor=e.targetAnchor,I=Us(e.props),k=I?s:N,z=I?Z:oe;if(l==="svg"||Ko(N)?l="svg":(l==="mathml"||Go(N))&&(l="mathml"),B?(v(e.dynamicChildren,B,k,r,i,l,c),Lo(e,t,!0)):a||f(e,t,k,z,r,i,l,c,!1),T)I?t.props&&e.props&&t.props.to!==e.props.to&&(t.props.to=e.props.to):ln(t,s,Z,u,1);else if((t.props&&t.props.to)!==(e.props&&e.props.to)){const se=t.target=no(t.props,_);se&&ln(t,se,null,u,0)}else I&&ln(t,N,oe,u,1);dn(t,T)}},remove(e,t,s,{um:o,o:{remove:r}},i){const{shapeFlag:l,children:c,anchor:a,targetStart:u,targetAnchor:d,target:f,props:v}=e;if(f&&(r(u),r(d)),i&&r(a),l&16){const g=i||!Us(v);for(let _=0;_<c.length;_++){const y=c[_];o(y,t,s,g,!!y.dynamicChildren)}}},move:ln,hydrate:ql};function ln(e,t,s,{o:{insert:o},m:r},i=2){i===0&&o(e.targetAnchor,t,s);const{el:l,anchor:c,shapeFlag:a,children:u,props:d}=e,f=i===2;if(f&&o(l,t,s),(!f||Us(d))&&a&16)for(let v=0;v<u.length;v++)r(u[v],t,s,2);f&&o(c,t,s)}function ql(e,t,s,o,r,i,{o:{nextSibling:l,parentNode:c,querySelector:a,insert:u,createText:d}},f){const v=t.target=no(t.props,a);if(v){const g=Us(t.props),_=v._lpa||v.firstChild;if(t.shapeFlag&16)if(g)t.anchor=f(l(e),t,c(e),s,o,r,i),t.targetStart=_,t.targetAnchor=_&&l(_);else{t.anchor=l(e);let y=_;for(;y;){if(y&&y.nodeType===8){if(y.data==="teleport start anchor")t.targetStart=y;else if(y.data==="teleport anchor"){t.targetAnchor=y,v._lpa=t.targetAnchor&&l(t.targetAnchor);break}}y=l(y)}t.targetAnchor||wi(v,t,d,u),f(_&&l(_),t,v,s,o,r,i)}dn(t,g)}return t.anchor&&l(t.anchor)}const Zl=mi;function dn(e,t){const s=e.ctx;if(s&&s.ut){let o,r;for(t?(o=e.el,r=e.anchor):(o=e.targetStart,r=e.targetAnchor);o&&o!==r;)o.nodeType===1&&o.setAttribute("data-v-owner",s.uid),o=o.nextSibling;s.ut()}}function wi(e,t,s,o){const r=t.targetStart=s(""),i=t.targetAnchor=s("");return r[gi]=i,e&&(o(r,e),o(i,e)),i}function Po(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Po(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Ie(e,t){return ce(e)?qe({name:e.name},t,{setup:e}):e}function yi(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function Vs(e,t,s,o,r=!1){if(ne(e)){e.forEach((_,y)=>Vs(_,t&&(ne(t)?t[y]:t),s,o,r));return}if(ws(o)&&!r){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&Vs(e,t,s,o.component.subTree);return}const i=o.shapeFlag&4?In(o.component):o.el,l=r?null:i,{i:c,r:a}=e,u=t&&t.r,d=c.refs===Se?c.refs={}:c.refs,f=c.setupState,v=ke(f),g=f===Se?()=>!1:_=>xe(v,_);if(u!=null&&u!==a&&(Re(u)?(d[u]=null,g(u)&&(f[u]=null)):We(u)&&(u.value=null)),ce(a))Xs(a,c,12,[l,d]);else{const _=Re(a),y=We(a);if(_||y){const w=()=>{if(e.f){const T=_?g(a)?f[a]:d[a]:a.value;r?ne(T)&&mo(T,i):ne(T)?T.includes(i)||T.push(i):_?(d[a]=[i],g(a)&&(f[a]=d[a])):(a.value=[i],e.k&&(d[e.k]=a.value))}else _?(d[a]=l,g(a)&&(f[a]=l)):y&&(a.value=l,e.k&&(d[e.k]=l))};l?(w.id=-1,Ye(w,s)):w()}}}Pn().requestIdleCallback;Pn().cancelIdleCallback;const ws=e=>!!e.type.__asyncLoader,bi=e=>e.type.__isKeepAlive;function Jl(e,t){_i(e,"a",t)}function Yl(e,t){_i(e,"da",t)}function _i(e,t,s=Ge){const o=e.__wdc||(e.__wdc=()=>{let r=s;for(;r;){if(r.isDeactivated)return;r=r.parent}return e()});if(Ln(t,o,s),s){let r=s.parent;for(;r&&r.parent;)bi(r.parent.vnode)&&Ql(o,t,s,r),r=r.parent}}function Ql(e,t,s,o){const r=Ln(t,e,o,!0);Ss(()=>{mo(o[t],r)},s)}function Ln(e,t,s=Ge,o=!1){if(s){const r=s[e]||(s[e]=[]),i=t.__weh||(t.__weh=(...l)=>{Rt();const c=en(s),a=xt(t,s,e,l);return c(),It(),a});return o?r.unshift(i):r.push(i),i}}const Vt=e=>(t,s=Ge)=>{(!qs||e==="sp")&&Ln(e,(...o)=>t(...o),s)},Xl=Vt("bm"),jt=Vt("m"),ea=Vt("bu"),ta=Vt("u"),sa=Vt("bum"),Ss=Vt("um"),na=Vt("sp"),oa=Vt("rtg"),ra=Vt("rtc");function ia(e,t=Ge){Ln("ec",e,t)}const la="components";function ft(e,t){return ca(la,e,!0,t)||e}const aa=Symbol.for("v-ndc");function ca(e,t,s=!0,o=!1){const r=Fe||Ge;if(r){const i=r.type;{const c=Ya(i,!1);if(c&&(c===t||c===dt(t)||c===En(dt(t))))return i}const l=Wo(r[e]||i[e],t)||Wo(r.appContext[e],t);return!l&&o?i:l}}function Wo(e,t){return e&&(e[t]||e[dt(t)]||e[En(dt(t))])}function Ve(e,t,s,o){let r;const i=s,l=ne(e);if(l||Re(e)){const c=l&&gs(e);let a=!1,u=!1;c&&(a=!ct(e),u=qt(e),e=An(e)),r=new Array(e.length);for(let d=0,f=e.length;d<f;d++)r[d]=t(a?u?wn(Be(e[d])):Be(e[d]):e[d],d,void 0,i)}else if(typeof e=="number"){r=new Array(e);for(let c=0;c<e;c++)r[c]=t(c+1,c,void 0,i)}else if(Pe(e))if(e[Symbol.iterator])r=Array.from(e,(c,a)=>t(c,a,void 0,i));else{const c=Object.keys(e);r=new Array(c.length);for(let a=0,u=c.length;a<u;a++){const d=c[a];r[a]=t(e[d],d,a,i)}}else r=[];return r}function ua(e,t,s={},o,r){if(Fe.ce||Fe.parent&&ws(Fe.parent)&&Fe.parent.ce)return s.name=t,b(),Xe(ae,null,[ue("slot",s,o)],64);let i=e[t];i&&i._c&&(i._d=!1),b();const l=i&&ki(i(s)),c=s.key||l&&l.key,a=Xe(ae,{key:(c&&!mt(c)?c:`_${t}`)+(!l&&o?"_fb":"")},l||[],l&&e._===1?64:-2);return i&&i._c&&(i._d=!0),a}function ki(e){return e.some(t=>Ws(t)?!(t.type===Ut||t.type===ae&&!ki(t.children)):!0)?e:null}const oo=e=>e?Bi(e)?In(e):oo(e.parent):null,js=qe(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>oo(e.parent),$root:e=>oo(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>xi(e),$forceUpdate:e=>e.f||(e.f=()=>{Eo(e.update)}),$nextTick:e=>e.n||(e.n=To.bind(e.proxy)),$watch:e=>Ma.bind(e)}),Kn=(e,t)=>e!==Se&&!e.__isScriptSetup&&xe(e,t),da={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:o,data:r,props:i,accessCache:l,type:c,appContext:a}=e;let u;if(t[0]!=="$"){const g=l[t];if(g!==void 0)switch(g){case 1:return o[t];case 2:return r[t];case 4:return s[t];case 3:return i[t]}else{if(Kn(o,t))return l[t]=1,o[t];if(r!==Se&&xe(r,t))return l[t]=2,r[t];if((u=e.propsOptions[0])&&xe(u,t))return l[t]=3,i[t];if(s!==Se&&xe(s,t))return l[t]=4,s[t];ro&&(l[t]=0)}}const d=js[t];let f,v;if(d)return t==="$attrs"&&Ke(e.attrs,"get",""),d(e);if((f=c.__cssModules)&&(f=f[t]))return f;if(s!==Se&&xe(s,t))return l[t]=4,s[t];if(v=a.config.globalProperties,xe(v,t))return v[t]},set({_:e},t,s){const{data:o,setupState:r,ctx:i}=e;return Kn(r,t)?(r[t]=s,!0):o!==Se&&xe(o,t)?(o[t]=s,!0):xe(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(i[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:o,appContext:r,propsOptions:i}},l){let c;return!!s[l]||e!==Se&&xe(e,l)||Kn(t,l)||(c=i[0])&&xe(c,l)||xe(o,l)||xe(js,l)||xe(r.config.globalProperties,l)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:xe(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function qo(e){return ne(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let ro=!0;function fa(e){const t=xi(e),s=e.proxy,o=e.ctx;ro=!1,t.beforeCreate&&Zo(t.beforeCreate,e,"bc");const{data:r,computed:i,methods:l,watch:c,provide:a,inject:u,created:d,beforeMount:f,mounted:v,beforeUpdate:g,updated:_,activated:y,deactivated:w,beforeDestroy:T,beforeUnmount:A,destroyed:U,unmounted:B,render:Z,renderTracked:N,renderTriggered:oe,errorCaptured:I,serverPrefetch:k,expose:z,inheritAttrs:se,components:H,directives:P,filters:le}=t;if(u&&pa(u,o,null),l)for(const be in l){const ge=l[be];ce(ge)&&(o[be]=ge.bind(s))}if(r){const be=r.call(s,s);Pe(be)&&(e.data=Me(be))}if(ro=!0,i)for(const be in i){const ge=i[be],nt=ce(ge)?ge.bind(s,s):ce(ge.get)?ge.get.bind(s,s):Ct,ot=!ce(ge)&&ce(ge.set)?ge.set.bind(s):Ct,et=Te({get:nt,set:ot});Object.defineProperty(o,be,{enumerable:!0,configurable:!0,get:()=>et.value,set:He=>et.value=He})}if(c)for(const be in c)Ci(c[be],o,s,be);if(a){const be=ce(a)?a.call(s):a;Reflect.ownKeys(be).forEach(ge=>{fn(ge,be[ge])})}d&&Zo(d,e,"c");function Ae(be,ge){ne(ge)?ge.forEach(nt=>be(nt.bind(s))):ge&&be(ge.bind(s))}if(Ae(Xl,f),Ae(jt,v),Ae(ea,g),Ae(ta,_),Ae(Jl,y),Ae(Yl,w),Ae(ia,I),Ae(ra,N),Ae(oa,oe),Ae(sa,A),Ae(Ss,B),Ae(na,k),ne(z))if(z.length){const be=e.exposed||(e.exposed={});z.forEach(ge=>{Object.defineProperty(be,ge,{get:()=>s[ge],set:nt=>s[ge]=nt,enumerable:!0})})}else e.exposed||(e.exposed={});Z&&e.render===Ct&&(e.render=Z),se!=null&&(e.inheritAttrs=se),H&&(e.components=H),P&&(e.directives=P),k&&yi(e)}function pa(e,t,s=Ct){ne(e)&&(e=io(e));for(const o in e){const r=e[o];let i;Pe(r)?"default"in r?i=gt(r.from||o,r.default,!0):i=gt(r.from||o):i=gt(r),We(i)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>i.value,set:l=>i.value=l}):t[o]=i}}function Zo(e,t,s){xt(ne(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,s)}function Ci(e,t,s,o){let r=o.includes(".")?Ui(s,o):()=>s[o];if(Re(e)){const i=t[e];ce(i)&&Ns(r,i)}else if(ce(e))Ns(r,e.bind(s));else if(Pe(e))if(ne(e))e.forEach(i=>Ci(i,t,s,o));else{const i=ce(e.handler)?e.handler.bind(s):t[e.handler];ce(i)&&Ns(r,i,e)}}function xi(e){const t=e.type,{mixins:s,extends:o}=t,{mixins:r,optionsCache:i,config:{optionMergeStrategies:l}}=e.appContext,c=i.get(t);let a;return c?a=c:!r.length&&!s&&!o?a=t:(a={},r.length&&r.forEach(u=>kn(a,u,l,!0)),kn(a,t,l)),Pe(t)&&i.set(t,a),a}function kn(e,t,s,o=!1){const{mixins:r,extends:i}=t;i&&kn(e,i,s,!0),r&&r.forEach(l=>kn(e,l,s,!0));for(const l in t)if(!(o&&l==="expose")){const c=ha[l]||s&&s[l];e[l]=c?c(e[l],t[l]):t[l]}return e}const ha={data:Jo,props:Yo,emits:Yo,methods:Ls,computed:Ls,beforeCreate:Je,created:Je,beforeMount:Je,mounted:Je,beforeUpdate:Je,updated:Je,beforeDestroy:Je,beforeUnmount:Je,destroyed:Je,unmounted:Je,activated:Je,deactivated:Je,errorCaptured:Je,serverPrefetch:Je,components:Ls,directives:Ls,watch:ga,provide:Jo,inject:va};function Jo(e,t){return t?e?function(){return qe(ce(e)?e.call(this,this):e,ce(t)?t.call(this,this):t)}:t:e}function va(e,t){return Ls(io(e),io(t))}function io(e){if(ne(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Je(e,t){return e?[...new Set([].concat(e,t))]:t}function Ls(e,t){return e?qe(Object.create(null),e,t):t}function Yo(e,t){return e?ne(e)&&ne(t)?[...new Set([...e,...t])]:qe(Object.create(null),qo(e),qo(t??{})):t}function ga(e,t){if(!e)return t;if(!t)return e;const s=qe(Object.create(null),e);for(const o in t)s[o]=Je(e[o],t[o]);return s}function $i(){return{app:null,config:{isNativeTag:rl,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let ma=0;function wa(e,t){return function(o,r=null){ce(o)||(o=qe({},o)),r!=null&&!Pe(r)&&(r=null);const i=$i(),l=new WeakSet,c=[];let a=!1;const u=i.app={_uid:ma++,_component:o,_props:r,_container:null,_context:i,_instance:null,version:Xa,get config(){return i.config},set config(d){},use(d,...f){return l.has(d)||(d&&ce(d.install)?(l.add(d),d.install(u,...f)):ce(d)&&(l.add(d),d(u,...f))),u},mixin(d){return i.mixins.includes(d)||i.mixins.push(d),u},component(d,f){return f?(i.components[d]=f,u):i.components[d]},directive(d,f){return f?(i.directives[d]=f,u):i.directives[d]},mount(d,f,v){if(!a){const g=u._ceVNode||ue(o,r);return g.appContext=i,v===!0?v="svg":v===!1&&(v=void 0),e(g,d,v),a=!0,u._container=d,d.__vue_app__=u,In(g.component)}},onUnmount(d){c.push(d)},unmount(){a&&(xt(c,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(d,f){return i.provides[d]=f,u},runWithContext(d){const f=ys;ys=u;try{return d()}finally{ys=f}}};return u}}let ys=null;function fn(e,t){if(Ge){let s=Ge.provides;const o=Ge.parent&&Ge.parent.provides;o===s&&(s=Ge.provides=Object.create(o)),s[e]=t}}function gt(e,t,s=!1){const o=Ga();if(o||ys){let r=ys?ys._context.provides:o?o.parent==null||o.ce?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(r&&e in r)return r[e];if(arguments.length>1)return s&&ce(t)?t.call(o&&o.proxy):t}}const Si={},Ti=()=>Object.create(Si),Ei=e=>Object.getPrototypeOf(e)===Si;function ya(e,t,s,o=!1){const r={},i=Ti();e.propsDefaults=Object.create(null),Pi(e,t,r,i);for(const l in e.propsOptions[0])l in r||(r[l]=void 0);s?e.props=o?r:ii(r):e.type.props?e.props=r:e.props=i,e.attrs=i}function ba(e,t,s,o){const{props:r,attrs:i,vnode:{patchFlag:l}}=e,c=ke(r),[a]=e.propsOptions;let u=!1;if((o||l>0)&&!(l&16)){if(l&8){const d=e.vnode.dynamicProps;for(let f=0;f<d.length;f++){let v=d[f];if(On(e.emitsOptions,v))continue;const g=t[v];if(a)if(xe(i,v))g!==i[v]&&(i[v]=g,u=!0);else{const _=dt(v);r[_]=lo(a,c,_,g,e,!1)}else g!==i[v]&&(i[v]=g,u=!0)}}}else{Pi(e,t,r,i)&&(u=!0);let d;for(const f in c)(!t||!xe(t,f)&&((d=Jt(f))===f||!xe(t,d)))&&(a?s&&(s[f]!==void 0||s[d]!==void 0)&&(r[f]=lo(a,c,f,void 0,e,!0)):delete r[f]);if(i!==c)for(const f in i)(!t||!xe(t,f))&&(delete i[f],u=!0)}u&&At(e.attrs,"set","")}function Pi(e,t,s,o){const[r,i]=e.propsOptions;let l=!1,c;if(t)for(let a in t){if(Os(a))continue;const u=t[a];let d;r&&xe(r,d=dt(a))?!i||!i.includes(d)?s[d]=u:(c||(c={}))[d]=u:On(e.emitsOptions,a)||(!(a in o)||u!==o[a])&&(o[a]=u,l=!0)}if(i){const a=ke(s),u=c||Se;for(let d=0;d<i.length;d++){const f=i[d];s[f]=lo(r,a,f,u[f],e,!xe(u,f))}}return l}function lo(e,t,s,o,r,i){const l=e[s];if(l!=null){const c=xe(l,"default");if(c&&o===void 0){const a=l.default;if(l.type!==Function&&!l.skipFactory&&ce(a)){const{propsDefaults:u}=r;if(s in u)o=u[s];else{const d=en(r);o=u[s]=a.call(null,t),d()}}else o=a;r.ce&&r.ce._setProp(s,o)}l[0]&&(i&&!c?o=!1:l[1]&&(o===""||o===Jt(s))&&(o=!0))}return o}const _a=new WeakMap;function Ai(e,t,s=!1){const o=s?_a:t.propsCache,r=o.get(e);if(r)return r;const i=e.props,l={},c=[];let a=!1;if(!ce(e)){const d=f=>{a=!0;const[v,g]=Ai(f,t,!0);qe(l,v),g&&c.push(...g)};!s&&t.mixins.length&&t.mixins.forEach(d),e.extends&&d(e.extends),e.mixins&&e.mixins.forEach(d)}if(!i&&!a)return Pe(e)&&o.set(e,hs),hs;if(ne(i))for(let d=0;d<i.length;d++){const f=dt(i[d]);Qo(f)&&(l[f]=Se)}else if(i)for(const d in i){const f=dt(d);if(Qo(f)){const v=i[d],g=l[f]=ne(v)||ce(v)?{type:v}:qe({},v),_=g.type;let y=!1,w=!0;if(ne(_))for(let T=0;T<_.length;++T){const A=_[T],U=ce(A)&&A.name;if(U==="Boolean"){y=!0;break}else U==="String"&&(w=!1)}else y=ce(_)&&_.name==="Boolean";g[0]=y,g[1]=w,(y||xe(g,"default"))&&c.push(f)}}const u=[l,c];return Pe(e)&&o.set(e,u),u}function Qo(e){return e[0]!=="$"&&!Os(e)}const Ao=e=>e==="_"||e==="__"||e==="_ctx"||e==="$stable",Mo=e=>ne(e)?e.map(_t):[_t(e)],ka=(e,t,s)=>{if(t._n)return t;const o=me((...r)=>Mo(t(...r)),s);return o._c=!1,o},Mi=(e,t,s)=>{const o=e._ctx;for(const r in e){if(Ao(r))continue;const i=e[r];if(ce(i))t[r]=ka(r,i,o);else if(i!=null){const l=Mo(i);t[r]=()=>l}}},Li=(e,t)=>{const s=Mo(t);e.slots.default=()=>s},Oi=(e,t,s)=>{for(const o in t)(s||!Ao(o))&&(e[o]=t[o])},Ca=(e,t,s)=>{const o=e.slots=Ti();if(e.vnode.shapeFlag&32){const r=t.__;r&&Qn(o,"__",r,!0);const i=t._;i?(Oi(o,t,s),s&&Qn(o,"_",i,!0)):Mi(t,o)}else t&&Li(e,t)},xa=(e,t,s)=>{const{vnode:o,slots:r}=e;let i=!0,l=Se;if(o.shapeFlag&32){const c=t._;c?s&&c===1?i=!1:Oi(r,t,s):(i=!t.$stable,Mi(t,r)),l=t}else t&&(Li(e,t),l={default:1});if(i)for(const c in r)!Ao(c)&&l[c]==null&&delete r[c]},Ye=ja;function $a(e){return Sa(e)}function Sa(e,t){const s=Pn();s.__VUE__=!0;const{insert:o,remove:r,patchProp:i,createElement:l,createText:c,createComment:a,setText:u,setElementText:d,parentNode:f,nextSibling:v,setScopeId:g=Ct,insertStaticContent:_}=e,y=(p,h,x,L=null,R=null,O=null,K=void 0,F=null,D=!!h.dynamicChildren)=>{if(p===h)return;p&&!As(p,h)&&(L=M(p),He(p,R,O,!0),p=null),h.patchFlag===-2&&(D=!1,h.dynamicChildren=null);const{type:V,ref:X,shapeFlag:W}=h;switch(V){case Rn:w(p,h,x,L);break;case Ut:T(p,h,x,L);break;case pn:p==null&&A(h,x,L,K);break;case ae:H(p,h,x,L,R,O,K,F,D);break;default:W&1?Z(p,h,x,L,R,O,K,F,D):W&6?P(p,h,x,L,R,O,K,F,D):(W&64||W&128)&&V.process(p,h,x,L,R,O,K,F,D,Y)}X!=null&&R?Vs(X,p&&p.ref,O,h||p,!h):X==null&&p&&p.ref!=null&&Vs(p.ref,null,O,p,!0)},w=(p,h,x,L)=>{if(p==null)o(h.el=c(h.children),x,L);else{const R=h.el=p.el;h.children!==p.children&&u(R,h.children)}},T=(p,h,x,L)=>{p==null?o(h.el=a(h.children||""),x,L):h.el=p.el},A=(p,h,x,L)=>{[p.el,p.anchor]=_(p.children,h,x,L,p.el,p.anchor)},U=({el:p,anchor:h},x,L)=>{let R;for(;p&&p!==h;)R=v(p),o(p,x,L),p=R;o(h,x,L)},B=({el:p,anchor:h})=>{let x;for(;p&&p!==h;)x=v(p),r(p),p=x;r(h)},Z=(p,h,x,L,R,O,K,F,D)=>{h.type==="svg"?K="svg":h.type==="math"&&(K="mathml"),p==null?N(h,x,L,R,O,K,F,D):k(p,h,R,O,K,F,D)},N=(p,h,x,L,R,O,K,F)=>{let D,V;const{props:X,shapeFlag:W,transition:Q,dirs:re}=p;if(D=p.el=l(p.type,O,X&&X.is,X),W&8?d(D,p.children):W&16&&I(p.children,D,null,L,R,Gn(p,O),K,F),re&&Xt(p,null,L,"created"),oe(D,p,p.scopeId,K,L),X){for(const we in X)we!=="value"&&!Os(we)&&i(D,we,null,X[we],O,L);"value"in X&&i(D,"value",null,X.value,O),(V=X.onVnodeBeforeMount)&&yt(V,L,p)}re&&Xt(p,null,L,"beforeMount");const he=Ta(R,Q);he&&Q.beforeEnter(D),o(D,h,x),((V=X&&X.onVnodeMounted)||he||re)&&Ye(()=>{V&&yt(V,L,p),he&&Q.enter(D),re&&Xt(p,null,L,"mounted")},R)},oe=(p,h,x,L,R)=>{if(x&&g(p,x),L)for(let O=0;O<L.length;O++)g(p,L[O]);if(R){let O=R.subTree;if(h===O||ji(O.type)&&(O.ssContent===h||O.ssFallback===h)){const K=R.vnode;oe(p,K,K.scopeId,K.slotScopeIds,R.parent)}}},I=(p,h,x,L,R,O,K,F,D=0)=>{for(let V=D;V<p.length;V++){const X=p[V]=F?Kt(p[V]):_t(p[V]);y(null,X,h,x,L,R,O,K,F)}},k=(p,h,x,L,R,O,K)=>{const F=h.el=p.el;let{patchFlag:D,dynamicChildren:V,dirs:X}=h;D|=p.patchFlag&16;const W=p.props||Se,Q=h.props||Se;let re;if(x&&es(x,!1),(re=Q.onVnodeBeforeUpdate)&&yt(re,x,h,p),X&&Xt(h,p,x,"beforeUpdate"),x&&es(x,!0),(W.innerHTML&&Q.innerHTML==null||W.textContent&&Q.textContent==null)&&d(F,""),V?z(p.dynamicChildren,V,F,x,L,Gn(h,R),O):K||ge(p,h,F,null,x,L,Gn(h,R),O,!1),D>0){if(D&16)se(F,W,Q,x,R);else if(D&2&&W.class!==Q.class&&i(F,"class",null,Q.class,R),D&4&&i(F,"style",W.style,Q.style,R),D&8){const he=h.dynamicProps;for(let we=0;we<he.length;we++){const ye=he[we],Ne=W[ye],ze=Q[ye];(ze!==Ne||ye==="value")&&i(F,ye,Ne,ze,R,x)}}D&1&&p.children!==h.children&&d(F,h.children)}else!K&&V==null&&se(F,W,Q,x,R);((re=Q.onVnodeUpdated)||X)&&Ye(()=>{re&&yt(re,x,h,p),X&&Xt(h,p,x,"updated")},L)},z=(p,h,x,L,R,O,K)=>{for(let F=0;F<h.length;F++){const D=p[F],V=h[F],X=D.el&&(D.type===ae||!As(D,V)||D.shapeFlag&198)?f(D.el):x;y(D,V,X,null,L,R,O,K,!0)}},se=(p,h,x,L,R)=>{if(h!==x){if(h!==Se)for(const O in h)!Os(O)&&!(O in x)&&i(p,O,h[O],null,R,L);for(const O in x){if(Os(O))continue;const K=x[O],F=h[O];K!==F&&O!=="value"&&i(p,O,F,K,R,L)}"value"in x&&i(p,"value",h.value,x.value,R)}},H=(p,h,x,L,R,O,K,F,D)=>{const V=h.el=p?p.el:c(""),X=h.anchor=p?p.anchor:c("");let{patchFlag:W,dynamicChildren:Q,slotScopeIds:re}=h;re&&(F=F?F.concat(re):re),p==null?(o(V,x,L),o(X,x,L),I(h.children||[],x,X,R,O,K,F,D)):W>0&&W&64&&Q&&p.dynamicChildren?(z(p.dynamicChildren,Q,x,R,O,K,F),(h.key!=null||R&&h===R.subTree)&&Lo(p,h,!0)):ge(p,h,x,X,R,O,K,F,D)},P=(p,h,x,L,R,O,K,F,D)=>{h.slotScopeIds=F,p==null?h.shapeFlag&512?R.ctx.activate(h,x,L,K,D):le(h,x,L,R,O,K,D):Oe(p,h,D)},le=(p,h,x,L,R,O,K)=>{const F=p.component=Ka(p,L,R);if(bi(p)&&(F.ctx.renderer=Y),Wa(F,!1,K),F.asyncDep){if(R&&R.registerDep(F,Ae,K),!p.el){const D=F.subTree=ue(Ut);T(null,D,h,x),p.placeholder=D.el}}else Ae(F,p,h,x,R,O,K)},Oe=(p,h,x)=>{const L=h.component=p.component;if(Ua(p,h,x))if(L.asyncDep&&!L.asyncResolved){be(L,h,x);return}else L.next=h,L.update();else h.el=p.el,L.vnode=h},Ae=(p,h,x,L,R,O,K)=>{const F=()=>{if(p.isMounted){let{next:W,bu:Q,u:re,parent:he,vnode:we}=p;{const Ze=Ri(p);if(Ze){W&&(W.el=we.el,be(p,W,K)),Ze.asyncDep.then(()=>{p.isUnmounted||F()});return}}let ye=W,Ne;es(p,!1),W?(W.el=we.el,be(p,W,K)):W=we,Q&&un(Q),(Ne=W.props&&W.props.onVnodeBeforeUpdate)&&yt(Ne,he,W,we),es(p,!0);const ze=er(p),at=p.subTree;p.subTree=ze,y(at,ze,f(at.el),M(at),p,R,O),W.el=ze.el,ye===null&&Va(p,ze.el),re&&Ye(re,R),(Ne=W.props&&W.props.onVnodeUpdated)&&Ye(()=>yt(Ne,he,W,we),R)}else{let W;const{el:Q,props:re}=h,{bm:he,m:we,parent:ye,root:Ne,type:ze}=p,at=ws(h);es(p,!1),he&&un(he),!at&&(W=re&&re.onVnodeBeforeMount)&&yt(W,ye,h),es(p,!0);{Ne.ce&&Ne.ce._def.shadowRoot!==!1&&Ne.ce._injectChildStyle(ze);const Ze=p.subTree=er(p);y(null,Ze,x,L,p,R,O),h.el=Ze.el}if(we&&Ye(we,R),!at&&(W=re&&re.onVnodeMounted)){const Ze=h;Ye(()=>yt(W,ye,Ze),R)}(h.shapeFlag&256||ye&&ws(ye.vnode)&&ye.vnode.shapeFlag&256)&&p.a&&Ye(p.a,R),p.isMounted=!0,h=x=L=null}};p.scope.on();const D=p.effect=new Gr(F);p.scope.off();const V=p.update=D.run.bind(D),X=p.job=D.runIfDirty.bind(D);X.i=p,X.id=p.uid,D.scheduler=()=>Eo(X),es(p,!0),V()},be=(p,h,x)=>{h.component=p;const L=p.vnode.props;p.vnode=h,p.next=null,ba(p,h.props,L,x),xa(p,h.children,x),Rt(),Ho(p),It()},ge=(p,h,x,L,R,O,K,F,D=!1)=>{const V=p&&p.children,X=p?p.shapeFlag:0,W=h.children,{patchFlag:Q,shapeFlag:re}=h;if(Q>0){if(Q&128){ot(V,W,x,L,R,O,K,F,D);return}else if(Q&256){nt(V,W,x,L,R,O,K,F,D);return}}re&8?(X&16&&tt(V,R,O),W!==V&&d(x,W)):X&16?re&16?ot(V,W,x,L,R,O,K,F,D):tt(V,R,O,!0):(X&8&&d(x,""),re&16&&I(W,x,L,R,O,K,F,D))},nt=(p,h,x,L,R,O,K,F,D)=>{p=p||hs,h=h||hs;const V=p.length,X=h.length,W=Math.min(V,X);let Q;for(Q=0;Q<W;Q++){const re=h[Q]=D?Kt(h[Q]):_t(h[Q]);y(p[Q],re,x,null,R,O,K,F,D)}V>X?tt(p,R,O,!0,!1,W):I(h,x,L,R,O,K,F,D,W)},ot=(p,h,x,L,R,O,K,F,D)=>{let V=0;const X=h.length;let W=p.length-1,Q=X-1;for(;V<=W&&V<=Q;){const re=p[V],he=h[V]=D?Kt(h[V]):_t(h[V]);if(As(re,he))y(re,he,x,null,R,O,K,F,D);else break;V++}for(;V<=W&&V<=Q;){const re=p[W],he=h[Q]=D?Kt(h[Q]):_t(h[Q]);if(As(re,he))y(re,he,x,null,R,O,K,F,D);else break;W--,Q--}if(V>W){if(V<=Q){const re=Q+1,he=re<X?h[re].el:L;for(;V<=Q;)y(null,h[V]=D?Kt(h[V]):_t(h[V]),x,he,R,O,K,F,D),V++}}else if(V>Q)for(;V<=W;)He(p[V],R,O,!0),V++;else{const re=V,he=V,we=new Map;for(V=he;V<=Q;V++){const m=h[V]=D?Kt(h[V]):_t(h[V]);m.key!=null&&we.set(m.key,V)}let ye,Ne=0;const ze=Q-he+1;let at=!1,Ze=0;const Qt=new Array(ze);for(V=0;V<ze;V++)Qt[V]=0;for(V=re;V<=W;V++){const m=p[V];if(Ne>=ze){He(m,R,O,!0);continue}let $;if(m.key!=null)$=we.get(m.key);else for(ye=he;ye<=Q;ye++)if(Qt[ye-he]===0&&As(m,h[ye])){$=ye;break}$===void 0?He(m,R,O,!0):(Qt[$-he]=V+1,$>=Ze?Ze=$:at=!0,y(m,h[$],x,null,R,O,K,F,D),Ne++)}const E=at?Ea(Qt):hs;for(ye=E.length-1,V=ze-1;V>=0;V--){const m=he+V,$=h[m],ie=h[m+1],De=m+1<X?ie.el||ie.placeholder:L;Qt[V]===0?y(null,$,x,De,R,O,K,F,D):at&&(ye<0||V!==E[ye]?et($,x,De,2):ye--)}}},et=(p,h,x,L,R=null)=>{const{el:O,type:K,transition:F,children:D,shapeFlag:V}=p;if(V&6){et(p.component.subTree,h,x,L);return}if(V&128){p.suspense.move(h,x,L);return}if(V&64){K.move(p,h,x,Y);return}if(K===ae){o(O,h,x);for(let W=0;W<D.length;W++)et(D[W],h,x,L);o(p.anchor,h,x);return}if(K===pn){U(p,h,x);return}if(L!==2&&V&1&&F)if(L===0)F.beforeEnter(O),o(O,h,x),Ye(()=>F.enter(O),R);else{const{leave:W,delayLeave:Q,afterLeave:re}=F,he=()=>{p.ctx.isUnmounted?r(O):o(O,h,x)},we=()=>{W(O,()=>{he(),re&&re()})};Q?Q(O,he,we):we()}else o(O,h,x)},He=(p,h,x,L=!1,R=!1)=>{const{type:O,props:K,ref:F,children:D,dynamicChildren:V,shapeFlag:X,patchFlag:W,dirs:Q,cacheIndex:re}=p;if(W===-2&&(R=!1),F!=null&&(Rt(),Vs(F,null,x,p,!0),It()),re!=null&&(h.renderCache[re]=void 0),X&256){h.ctx.deactivate(p);return}const he=X&1&&Q,we=!ws(p);let ye;if(we&&(ye=K&&K.onVnodeBeforeUnmount)&&yt(ye,h,p),X&6)cs(p.component,x,L);else{if(X&128){p.suspense.unmount(x,L);return}he&&Xt(p,null,h,"beforeUnmount"),X&64?p.type.remove(p,h,x,Y,L):V&&!V.hasOnce&&(O!==ae||W>0&&W&64)?tt(V,h,x,!1,!0):(O===ae&&W&384||!R&&X&16)&&tt(D,h,x),L&&Dt(p)}(we&&(ye=K&&K.onVnodeUnmounted)||he)&&Ye(()=>{ye&&yt(ye,h,p),he&&Xt(p,null,h,"unmounted")},x)},Dt=p=>{const{type:h,el:x,anchor:L,transition:R}=p;if(h===ae){Bt(x,L);return}if(h===pn){B(p);return}const O=()=>{r(x),R&&!R.persisted&&R.afterLeave&&R.afterLeave()};if(p.shapeFlag&1&&R&&!R.persisted){const{leave:K,delayLeave:F}=R,D=()=>K(x,O);F?F(p.el,O,D):D()}else O()},Bt=(p,h)=>{let x;for(;p!==h;)x=v(p),r(p),p=x;r(h)},cs=(p,h,x)=>{const{bum:L,scope:R,job:O,subTree:K,um:F,m:D,a:V,parent:X,slots:{__:W}}=p;Xo(D),Xo(V),L&&un(L),X&&ne(W)&&W.forEach(Q=>{X.renderCache[Q]=void 0}),R.stop(),O&&(O.flags|=8,He(K,p,h,x)),F&&Ye(F,h),Ye(()=>{p.isUnmounted=!0},h),h&&h.pendingBranch&&!h.isUnmounted&&p.asyncDep&&!p.asyncResolved&&p.suspenseId===h.pendingId&&(h.deps--,h.deps===0&&h.resolve())},tt=(p,h,x,L=!1,R=!1,O=0)=>{for(let K=O;K<p.length;K++)He(p[K],h,x,L,R)},M=p=>{if(p.shapeFlag&6)return M(p.component.subTree);if(p.shapeFlag&128)return p.suspense.next();const h=v(p.anchor||p.el),x=h&&h[gi];return x?v(x):h};let q=!1;const G=(p,h,x)=>{p==null?h._vnode&&He(h._vnode,null,null,!0):y(h._vnode||null,p,h,null,null,null,x),h._vnode=p,q||(q=!0,Ho(),pi(),q=!1)},Y={p:y,um:He,m:et,r:Dt,mt:le,mc:I,pc:ge,pbc:z,n:M,o:e};return{render:G,hydrate:void 0,createApp:wa(G)}}function Gn({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function es({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Ta(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Lo(e,t,s=!1){const o=e.children,r=t.children;if(ne(o)&&ne(r))for(let i=0;i<o.length;i++){const l=o[i];let c=r[i];c.shapeFlag&1&&!c.dynamicChildren&&((c.patchFlag<=0||c.patchFlag===32)&&(c=r[i]=Kt(r[i]),c.el=l.el),!s&&c.patchFlag!==-2&&Lo(l,c)),c.type===Rn&&(c.el=l.el),c.type===Ut&&!c.el&&(c.el=l.el)}}function Ea(e){const t=e.slice(),s=[0];let o,r,i,l,c;const a=e.length;for(o=0;o<a;o++){const u=e[o];if(u!==0){if(r=s[s.length-1],e[r]<u){t[o]=r,s.push(o);continue}for(i=0,l=s.length-1;i<l;)c=i+l>>1,e[s[c]]<u?i=c+1:l=c;u<e[s[i]]&&(i>0&&(t[o]=s[i-1]),s[i]=o)}}for(i=s.length,l=s[i-1];i-- >0;)s[i]=l,l=t[l];return s}function Ri(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:Ri(t)}function Xo(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Pa=Symbol.for("v-scx"),Aa=()=>gt(Pa);function Ns(e,t,s){return Ii(e,t,s)}function Ii(e,t,s=Se){const{immediate:o,deep:r,flush:i,once:l}=s,c=qe({},s),a=t&&o||!t&&i!=="post";let u;if(qs){if(i==="sync"){const g=Aa();u=g.__watcherHandles||(g.__watcherHandles=[])}else if(!a){const g=()=>{};return g.stop=Ct,g.resume=Ct,g.pause=Ct,g}}const d=Ge;c.call=(g,_,y)=>xt(g,d,_,y);let f=!1;i==="post"?c.scheduler=g=>{Ye(g,d&&d.suspense)}:i!=="sync"&&(f=!0,c.scheduler=(g,_)=>{_?g():Eo(g)}),c.augmentJob=g=>{t&&(g.flags|=4),f&&(g.flags|=2,d&&(g.id=d.uid,g.i=d))};const v=Hl(e,t,c);return qs&&(u?u.push(v):a&&v()),v}function Ma(e,t,s){const o=this.proxy,r=Re(e)?e.includes(".")?Ui(o,e):()=>o[e]:e.bind(o,o);let i;ce(t)?i=t:(i=t.handler,s=t);const l=en(this),c=Ii(r,i.bind(o),s);return l(),c}function Ui(e,t){const s=t.split(".");return()=>{let o=e;for(let r=0;r<s.length&&o;r++)o=o[s[r]];return o}}const La=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${dt(t)}Modifiers`]||e[`${Jt(t)}Modifiers`];function Oa(e,t,...s){if(e.isUnmounted)return;const o=e.vnode.props||Se;let r=s;const i=t.startsWith("update:"),l=i&&La(o,t.slice(7));l&&(l.trim&&(r=s.map(d=>Re(d)?d.trim():d)),l.number&&(r=s.map(mn)));let c,a=o[c=Dn(t)]||o[c=Dn(dt(t))];!a&&i&&(a=o[c=Dn(Jt(t))]),a&&xt(a,e,6,r);const u=o[c+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[c])return;e.emitted[c]=!0,xt(u,e,6,r)}}function Vi(e,t,s=!1){const o=t.emitsCache,r=o.get(e);if(r!==void 0)return r;const i=e.emits;let l={},c=!1;if(!ce(e)){const a=u=>{const d=Vi(u,t,!0);d&&(c=!0,qe(l,d))};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!i&&!c?(Pe(e)&&o.set(e,null),null):(ne(i)?i.forEach(a=>l[a]=null):qe(l,i),Pe(e)&&o.set(e,l),l)}function On(e,t){return!e||!Sn(t)?!1:(t=t.slice(2).replace(/Once$/,""),xe(e,t[0].toLowerCase()+t.slice(1))||xe(e,Jt(t))||xe(e,t))}function er(e){const{type:t,vnode:s,proxy:o,withProxy:r,propsOptions:[i],slots:l,attrs:c,emit:a,render:u,renderCache:d,props:f,data:v,setupState:g,ctx:_,inheritAttrs:y}=e,w=_n(e);let T,A;try{if(s.shapeFlag&4){const B=r||o,Z=B;T=_t(u.call(Z,B,d,f,g,v,_)),A=c}else{const B=t;T=_t(B.length>1?B(f,{attrs:c,slots:l,emit:a}):B(f,null)),A=t.props?c:Ra(c)}}catch(B){Ds.length=0,Mn(B,e,1),T=ue(Ut)}let U=T;if(A&&y!==!1){const B=Object.keys(A),{shapeFlag:Z}=U;B.length&&Z&7&&(i&&B.some(go)&&(A=Ia(A,i)),U=bs(U,A,!1,!0))}return s.dirs&&(U=bs(U,null,!1,!0),U.dirs=U.dirs?U.dirs.concat(s.dirs):s.dirs),s.transition&&Po(U,s.transition),T=U,_n(w),T}const Ra=e=>{let t;for(const s in e)(s==="class"||s==="style"||Sn(s))&&((t||(t={}))[s]=e[s]);return t},Ia=(e,t)=>{const s={};for(const o in e)(!go(o)||!(o.slice(9)in t))&&(s[o]=e[o]);return s};function Ua(e,t,s){const{props:o,children:r,component:i}=e,{props:l,children:c,patchFlag:a}=t,u=i.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&a>=0){if(a&1024)return!0;if(a&16)return o?tr(o,l,u):!!l;if(a&8){const d=t.dynamicProps;for(let f=0;f<d.length;f++){const v=d[f];if(l[v]!==o[v]&&!On(u,v))return!0}}}else return(r||c)&&(!c||!c.$stable)?!0:o===l?!1:o?l?tr(o,l,u):!0:!!l;return!1}function tr(e,t,s){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let r=0;r<o.length;r++){const i=o[r];if(t[i]!==e[i]&&!On(s,i))return!0}return!1}function Va({vnode:e,parent:t},s){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=s,t=t.parent;else break}}const ji=e=>e.__isSuspense;function ja(e,t){t&&t.pendingBranch?ne(e)?t.effects.push(...e):t.effects.push(e):Gl(e)}const ae=Symbol.for("v-fgt"),Rn=Symbol.for("v-txt"),Ut=Symbol.for("v-cmt"),pn=Symbol.for("v-stc"),Ds=[];let it=null;function b(e=!1){Ds.push(it=e?null:[])}function Na(){Ds.pop(),it=Ds[Ds.length-1]||null}let Gs=1;function sr(e,t=!1){Gs+=e,e<0&&it&&t&&(it.hasOnce=!0)}function Ni(e){return e.dynamicChildren=Gs>0?it||hs:null,Na(),Gs>0&&it&&it.push(e),e}function C(e,t,s,o,r,i){return Ni(n(e,t,s,o,r,i,!0))}function Xe(e,t,s,o,r){return Ni(ue(e,t,s,o,r,!0))}function Ws(e){return e?e.__v_isVNode===!0:!1}function As(e,t){return e.type===t.type&&e.key===t.key}const Di=({key:e})=>e??null,hn=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?Re(e)||We(e)||ce(e)?{i:Fe,r:e,k:t,f:!!s}:e:null);function n(e,t=null,s=null,o=0,r=null,i=e===ae?0:1,l=!1,c=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Di(t),ref:t&&hn(t),scopeId:vi,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:i,patchFlag:o,dynamicProps:r,dynamicChildren:null,appContext:null,ctx:Fe};return c?(Oo(a,s),i&128&&e.normalize(a)):s&&(a.shapeFlag|=Re(s)?8:16),Gs>0&&!l&&it&&(a.patchFlag>0||i&6)&&a.patchFlag!==32&&it.push(a),a}const ue=Da;function Da(e,t=null,s=null,o=0,r=null,i=!1){if((!e||e===aa)&&(e=Ut),Ws(e)){const c=bs(e,t,!0);return s&&Oo(c,s),Gs>0&&!i&&it&&(c.shapeFlag&6?it[it.indexOf(e)]=c:it.push(c)),c.patchFlag=-2,c}if(Qa(e)&&(e=e.__vccOpts),t){t=Ba(t);let{class:c,style:a}=t;c&&!Re(c)&&(t.class=pe(c)),Pe(a)&&(So(a)&&!ne(a)&&(a=qe({},a)),t.style=Ot(a))}const l=Re(e)?1:ji(e)?128:Wl(e)?64:Pe(e)?4:ce(e)?2:0;return n(e,t,s,o,r,l,i,!0)}function Ba(e){return e?So(e)||Ei(e)?qe({},e):e:null}function bs(e,t,s=!1,o=!1){const{props:r,ref:i,patchFlag:l,children:c,transition:a}=e,u=t?Fa(r||{},t):r,d={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&Di(u),ref:t&&t.ref?s&&i?ne(i)?i.concat(hn(t)):[i,hn(t)]:hn(t):i,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:c,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ae?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&bs(e.ssContent),ssFallback:e.ssFallback&&bs(e.ssFallback),placeholder:e.placeholder,el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&o&&Po(d,a.clone(d)),d}function ee(e=" ",t=0){return ue(Rn,null,e,t)}function Le(e,t){const s=ue(pn,null,e);return s.staticCount=t,s}function J(e="",t=!1){return t?(b(),Xe(Ut,null,e)):ue(Ut,null,e)}function _t(e){return e==null||typeof e=="boolean"?ue(Ut):ne(e)?ue(ae,null,e.slice()):Ws(e)?Kt(e):ue(Rn,null,String(e))}function Kt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:bs(e)}function Oo(e,t){let s=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(ne(t))s=16;else if(typeof t=="object")if(o&65){const r=t.default;r&&(r._c&&(r._d=!1),Oo(e,r()),r._c&&(r._d=!0));return}else{s=32;const r=t._;!r&&!Ei(t)?t._ctx=Fe:r===3&&Fe&&(Fe.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else ce(t)?(t={default:t,_ctx:Fe},s=32):(t=String(t),o&64?(s=16,t=[ee(t)]):s=8);e.children=t,e.shapeFlag|=s}function Fa(...e){const t={};for(let s=0;s<e.length;s++){const o=e[s];for(const r in o)if(r==="class")t.class!==o.class&&(t.class=pe([t.class,o.class]));else if(r==="style")t.style=Ot([t.style,o.style]);else if(Sn(r)){const i=t[r],l=o[r];l&&i!==l&&!(ne(i)&&i.includes(l))&&(t[r]=i?[].concat(i,l):l)}else r!==""&&(t[r]=o[r])}return t}function yt(e,t,s,o=null){xt(e,t,7,[s,o])}const Ha=$i();let za=0;function Ka(e,t,s){const o=e.type,r=(t?t.appContext:e.appContext)||Ha,i={uid:za++,vnode:e,type:o,parent:t,appContext:r,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Kr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(r.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Ai(o,r),emitsOptions:Vi(o,r),emit:null,emitted:null,propsDefaults:Se,inheritAttrs:o.inheritAttrs,ctx:Se,data:Se,props:Se,attrs:Se,slots:Se,refs:Se,setupState:Se,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return i.ctx={_:i},i.root=t?t.root:i,i.emit=Oa.bind(null,i),e.ce&&e.ce(i),i}let Ge=null;const Ga=()=>Ge||Fe;let Cn,ao;{const e=Pn(),t=(s,o)=>{let r;return(r=e[s])||(r=e[s]=[]),r.push(o),i=>{r.length>1?r.forEach(l=>l(i)):r[0](i)}};Cn=t("__VUE_INSTANCE_SETTERS__",s=>Ge=s),ao=t("__VUE_SSR_SETTERS__",s=>qs=s)}const en=e=>{const t=Ge;return Cn(e),e.scope.on(),()=>{e.scope.off(),Cn(t)}},nr=()=>{Ge&&Ge.scope.off(),Cn(null)};function Bi(e){return e.vnode.shapeFlag&4}let qs=!1;function Wa(e,t=!1,s=!1){t&&ao(t);const{props:o,children:r}=e.vnode,i=Bi(e);ya(e,o,i,t),Ca(e,r,s||t);const l=i?qa(e,t):void 0;return t&&ao(!1),l}function qa(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,da);const{setup:o}=s;if(o){Rt();const r=e.setupContext=o.length>1?Ja(e):null,i=en(e),l=Xs(o,e,0,[e.props,r]),c=Nr(l);if(It(),i(),(c||e.sp)&&!ws(e)&&yi(e),c){if(l.then(nr,nr),t)return l.then(a=>{or(e,a)}).catch(a=>{Mn(a,e,0)});e.asyncDep=l}else or(e,l)}else Fi(e)}function or(e,t,s){ce(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Pe(t)&&(e.setupState=ui(t)),Fi(e)}function Fi(e,t,s){const o=e.type;e.render||(e.render=o.render||Ct);{const r=en(e);Rt();try{fa(e)}finally{It(),r()}}}const Za={get(e,t){return Ke(e,"get",""),e[t]}};function Ja(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Za),slots:e.slots,emit:e.emit,expose:t}}function In(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(ui(ai(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in js)return js[s](e)},has(t,s){return s in t||s in js}})):e.proxy}function Ya(e,t=!0){return ce(e)?e.displayName||e.name:e.name||t&&e.__name}function Qa(e){return ce(e)&&"__vccOpts"in e}const Te=(e,t)=>Bl(e,t,qs);function Hi(e,t,s){const o=arguments.length;return o===2?Pe(t)&&!ne(t)?Ws(t)?ue(e,null,[t]):ue(e,t):ue(e,null,t):(o>3?s=Array.prototype.slice.call(arguments,2):o===3&&Ws(s)&&(s=[s]),ue(e,t,s))}const Xa="3.5.18";/**
* @vue/runtime-dom v3.5.18
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let co;const rr=typeof window<"u"&&window.trustedTypes;if(rr)try{co=rr.createPolicy("vue",{createHTML:e=>e})}catch{}const zi=co?e=>co.createHTML(e):e=>e,ec="http://www.w3.org/2000/svg",tc="http://www.w3.org/1998/Math/MathML",Pt=typeof document<"u"?document:null,ir=Pt&&Pt.createElement("template"),sc={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,o)=>{const r=t==="svg"?Pt.createElementNS(ec,e):t==="mathml"?Pt.createElementNS(tc,e):s?Pt.createElement(e,{is:s}):Pt.createElement(e);return e==="select"&&o&&o.multiple!=null&&r.setAttribute("multiple",o.multiple),r},createText:e=>Pt.createTextNode(e),createComment:e=>Pt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Pt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,o,r,i){const l=s?s.previousSibling:t.lastChild;if(r&&(r===i||r.nextSibling))for(;t.insertBefore(r.cloneNode(!0),s),!(r===i||!(r=r.nextSibling)););else{ir.innerHTML=zi(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const c=ir.content;if(o==="svg"||o==="mathml"){const a=c.firstChild;for(;a.firstChild;)c.appendChild(a.firstChild);c.removeChild(a)}t.insertBefore(c,s)}return[l?l.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},nc=Symbol("_vtc");function oc(e,t,s){const o=e[nc];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const lr=Symbol("_vod"),rc=Symbol("_vsh"),ic=Symbol(""),lc=/(^|;)\s*display\s*:/;function ac(e,t,s){const o=e.style,r=Re(s);let i=!1;if(s&&!r){if(t)if(Re(t))for(const l of t.split(";")){const c=l.slice(0,l.indexOf(":")).trim();s[c]==null&&vn(o,c,"")}else for(const l in t)s[l]==null&&vn(o,l,"");for(const l in s)l==="display"&&(i=!0),vn(o,l,s[l])}else if(r){if(t!==s){const l=o[ic];l&&(s+=";"+l),o.cssText=s,i=lc.test(s)}}else t&&e.removeAttribute("style");lr in e&&(e[lr]=i?o.display:"",e[rc]&&(o.display="none"))}const ar=/\s*!important$/;function vn(e,t,s){if(ne(s))s.forEach(o=>vn(e,t,o));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const o=cc(e,t);ar.test(s)?e.setProperty(Jt(o),s.replace(ar,""),"important"):e[o]=s}}const cr=["Webkit","Moz","ms"],Wn={};function cc(e,t){const s=Wn[t];if(s)return s;let o=dt(t);if(o!=="filter"&&o in e)return Wn[t]=o;o=En(o);for(let r=0;r<cr.length;r++){const i=cr[r]+o;if(i in e)return Wn[t]=i}return t}const ur="http://www.w3.org/1999/xlink";function dr(e,t,s,o,r,i=vl(t)){o&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(ur,t.slice(6,t.length)):e.setAttributeNS(ur,t,s):s==null||i&&!Fr(s)?e.removeAttribute(t):e.setAttribute(t,i?"":mt(s)?String(s):s)}function fr(e,t,s,o,r){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?zi(s):s);return}const i=e.tagName;if(t==="value"&&i!=="PROGRESS"&&!i.includes("-")){const c=i==="OPTION"?e.getAttribute("value")||"":e.value,a=s==null?e.type==="checkbox"?"on":"":String(s);(c!==a||!("_value"in e))&&(e.value=a),s==null&&e.removeAttribute(t),e._value=s;return}let l=!1;if(s===""||s==null){const c=typeof e[t];c==="boolean"?s=Fr(s):s==null&&c==="string"?(s="",l=!0):c==="number"&&(s=0,l=!0)}try{e[t]=s}catch{}l&&e.removeAttribute(r||t)}function Lt(e,t,s,o){e.addEventListener(t,s,o)}function uc(e,t,s,o){e.removeEventListener(t,s,o)}const pr=Symbol("_vei");function dc(e,t,s,o,r=null){const i=e[pr]||(e[pr]={}),l=i[t];if(o&&l)l.value=o;else{const[c,a]=fc(t);if(o){const u=i[t]=vc(o,r);Lt(e,c,u,a)}else l&&(uc(e,c,l,a),i[t]=void 0)}}const hr=/(?:Once|Passive|Capture)$/;function fc(e){let t;if(hr.test(e)){t={};let o;for(;o=e.match(hr);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Jt(e.slice(2)),t]}let qn=0;const pc=Promise.resolve(),hc=()=>qn||(pc.then(()=>qn=0),qn=Date.now());function vc(e,t){const s=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=s.attached)return;xt(gc(o,s.value),t,5,[o])};return s.value=e,s.attached=hc(),s}function gc(e,t){if(ne(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(o=>r=>!r._stopped&&o&&o(r))}else return t}const vr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,mc=(e,t,s,o,r,i)=>{const l=r==="svg";t==="class"?oc(e,o,l):t==="style"?ac(e,s,o):Sn(t)?go(t)||dc(e,t,s,o,i):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):wc(e,t,o,l))?(fr(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&dr(e,t,o,l,i,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Re(o))?fr(e,dt(t),o,i,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),dr(e,t,o,l))};function wc(e,t,s,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&vr(t)&&ce(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const r=e.tagName;if(r==="IMG"||r==="VIDEO"||r==="CANVAS"||r==="SOURCE")return!1}return vr(t)&&Re(s)?!1:t in e}const Zt=e=>{const t=e.props["onUpdate:modelValue"]||!1;return ne(t)?s=>un(t,s):t};function yc(e){e.target.composing=!0}function gr(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const ut=Symbol("_assign"),de={created(e,{modifiers:{lazy:t,trim:s,number:o}},r){e[ut]=Zt(r);const i=o||r.props&&r.props.type==="number";Lt(e,t?"change":"input",l=>{if(l.target.composing)return;let c=e.value;s&&(c=c.trim()),i&&(c=mn(c)),e[ut](c)}),s&&Lt(e,"change",()=>{e.value=e.value.trim()}),t||(Lt(e,"compositionstart",yc),Lt(e,"compositionend",gr),Lt(e,"change",gr))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:o,trim:r,number:i}},l){if(e[ut]=Zt(l),e.composing)return;const c=(i||e.type==="number")&&!/^0\d/.test(e.value)?mn(e.value):e.value,a=t??"";c!==a&&(document.activeElement===e&&e.type!=="range"&&(o&&t===s||r&&e.value.trim()===a)||(e.value=a))}},Zs={deep:!0,created(e,t,s){e[ut]=Zt(s),Lt(e,"change",()=>{const o=e._modelValue,r=_s(e),i=e.checked,l=e[ut];if(ne(o)){const c=yo(o,r),a=c!==-1;if(i&&!a)l(o.concat(r));else if(!i&&a){const u=[...o];u.splice(c,1),l(u)}}else if($s(o)){const c=new Set(o);i?c.add(r):c.delete(r),l(c)}else l(Ki(e,i))})},mounted:mr,beforeUpdate(e,t,s){e[ut]=Zt(s),mr(e,t,s)}};function mr(e,{value:t,oldValue:s},o){e._modelValue=t;let r;if(ne(t))r=yo(t,o.props.value)>-1;else if($s(t))r=t.has(o.props.value);else{if(t===s)return;r=ls(t,Ki(e,!0))}e.checked!==r&&(e.checked=r)}const bc={created(e,{value:t},s){e.checked=ls(t,s.props.value),e[ut]=Zt(s),Lt(e,"change",()=>{e[ut](_s(e))})},beforeUpdate(e,{value:t,oldValue:s},o){e[ut]=Zt(o),t!==s&&(e.checked=ls(t,o.props.value))}},ns={deep:!0,created(e,{value:t,modifiers:{number:s}},o){const r=$s(t);Lt(e,"change",()=>{const i=Array.prototype.filter.call(e.options,l=>l.selected).map(l=>s?mn(_s(l)):_s(l));e[ut](e.multiple?r?new Set(i):i:i[0]),e._assigning=!0,To(()=>{e._assigning=!1})}),e[ut]=Zt(o)},mounted(e,{value:t}){wr(e,t)},beforeUpdate(e,t,s){e[ut]=Zt(s)},updated(e,{value:t}){e._assigning||wr(e,t)}};function wr(e,t){const s=e.multiple,o=ne(t);if(!(s&&!o&&!$s(t))){for(let r=0,i=e.options.length;r<i;r++){const l=e.options[r],c=_s(l);if(s)if(o){const a=typeof c;a==="string"||a==="number"?l.selected=t.some(u=>String(u)===String(c)):l.selected=yo(t,c)>-1}else l.selected=t.has(c);else if(ls(_s(l),t)){e.selectedIndex!==r&&(e.selectedIndex=r);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function _s(e){return"_value"in e?e._value:e.value}function Ki(e,t){const s=t?"_trueValue":"_falseValue";return s in e?e[s]:t}const ks={created(e,t,s){an(e,t,s,null,"created")},mounted(e,t,s){an(e,t,s,null,"mounted")},beforeUpdate(e,t,s,o){an(e,t,s,o,"beforeUpdate")},updated(e,t,s,o){an(e,t,s,o,"updated")}};function _c(e,t){switch(e){case"SELECT":return ns;case"TEXTAREA":return de;default:switch(t){case"checkbox":return Zs;case"radio":return bc;default:return de}}}function an(e,t,s,o,r){const l=_c(e.tagName,s.props&&s.props.type)[r];l&&l(e,t,s,o)}const kc=["ctrl","shift","alt","meta"],Cc={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>kc.some(s=>e[`${s}Key`]&&!t.includes(s))},kt=(e,t)=>{const s=e._withMods||(e._withMods={}),o=t.join(".");return s[o]||(s[o]=(r,...i)=>{for(let l=0;l<t.length;l++){const c=Cc[t[l]];if(c&&c(r,t))return}return e(r,...i)})},xc={esc:"escape",space:" ",up:"arrow-up",left:"arrow-left",right:"arrow-right",down:"arrow-down",delete:"backspace"},xn=(e,t)=>{const s=e._withKeys||(e._withKeys={}),o=t.join(".");return s[o]||(s[o]=r=>{if(!("key"in r))return;const i=Jt(r.key);if(t.some(l=>l===i||xc[l]===i))return e(r)})},$c=qe({patchProp:mc},sc);let yr;function Sc(){return yr||(yr=$a($c))}const Tc=(...e)=>{const t=Sc().createApp(...e),{mount:s}=t;return t.mount=o=>{const r=Pc(o);if(!r)return;const i=t._component;!ce(i)&&!i.render&&!i.template&&(i.template=r.innerHTML),r.nodeType===1&&(r.textContent="");const l=s(r,!1,Ec(r));return r instanceof Element&&(r.removeAttribute("v-cloak"),r.setAttribute("data-v-app","")),l},t};function Ec(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Pc(e){return Re(e)?document.querySelector(e):e}/*!
 * pinia v3.0.3
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */const Ac=Symbol();var br;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(br||(br={}));function Mc(){const e=ml(!0),t=e.run(()=>j({}));let s=[],o=[];const r=ai({install(i){r._a=i,i.provide(Ac,r),i.config.globalProperties.$pinia=r,o.forEach(l=>s.push(l)),o=[]},use(i){return this._a?s.push(i):o.push(i),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return r}const Ue=(e,t)=>{const s=e.__vccOpts||e;for(const[o,r]of t)s[o]=r;return s},Lc={},Oc={id:"app"};function Rc(e,t){const s=ft("router-view");return b(),C("div",Oc,[ue(s)])}const Ic=Ue(Lc,[["render",Rc]]);/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const ps=typeof document<"u";function Gi(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function Uc(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&Gi(e.default)}const _e=Object.assign;function Zn(e,t){const s={};for(const o in t){const r=t[o];s[o]=wt(r)?r.map(e):e(r)}return s}const Bs=()=>{},wt=Array.isArray,Wi=/#/g,Vc=/&/g,jc=/\//g,Nc=/=/g,Dc=/\?/g,qi=/\+/g,Bc=/%5B/g,Fc=/%5D/g,Zi=/%5E/g,Hc=/%60/g,Ji=/%7B/g,zc=/%7C/g,Yi=/%7D/g,Kc=/%20/g;function Ro(e){return encodeURI(""+e).replace(zc,"|").replace(Bc,"[").replace(Fc,"]")}function Gc(e){return Ro(e).replace(Ji,"{").replace(Yi,"}").replace(Zi,"^")}function uo(e){return Ro(e).replace(qi,"%2B").replace(Kc,"+").replace(Wi,"%23").replace(Vc,"%26").replace(Hc,"`").replace(Ji,"{").replace(Yi,"}").replace(Zi,"^")}function Wc(e){return uo(e).replace(Nc,"%3D")}function qc(e){return Ro(e).replace(Wi,"%23").replace(Dc,"%3F")}function Zc(e){return e==null?"":qc(e).replace(jc,"%2F")}function Js(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const Jc=/\/$/,Yc=e=>e.replace(Jc,"");function Jn(e,t,s="/"){let o,r={},i="",l="";const c=t.indexOf("#");let a=t.indexOf("?");return c<a&&c>=0&&(a=-1),a>-1&&(o=t.slice(0,a),i=t.slice(a+1,c>-1?c:t.length),r=e(i)),c>-1&&(o=o||t.slice(0,c),l=t.slice(c,t.length)),o=tu(o??t,s),{fullPath:o+(i&&"?")+i+l,path:o,query:r,hash:Js(l)}}function Qc(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function _r(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Xc(e,t,s){const o=t.matched.length-1,r=s.matched.length-1;return o>-1&&o===r&&Cs(t.matched[o],s.matched[r])&&Qi(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Cs(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Qi(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!eu(e[s],t[s]))return!1;return!0}function eu(e,t){return wt(e)?kr(e,t):wt(t)?kr(t,e):e===t}function kr(e,t){return wt(t)?e.length===t.length&&e.every((s,o)=>s===t[o]):e.length===1&&e[0]===t}function tu(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),o=e.split("/"),r=o[o.length-1];(r===".."||r===".")&&o.push("");let i=s.length-1,l,c;for(l=0;l<o.length;l++)if(c=o[l],c!==".")if(c==="..")i>1&&i--;else break;return s.slice(0,i).join("/")+"/"+o.slice(l).join("/")}const Ft={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Ys;(function(e){e.pop="pop",e.push="push"})(Ys||(Ys={}));var Fs;(function(e){e.back="back",e.forward="forward",e.unknown=""})(Fs||(Fs={}));function su(e){if(!e)if(ps){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),Yc(e)}const nu=/^[^#]+#/;function ou(e,t){return e.replace(nu,"#")+t}function ru(e,t){const s=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-s.left-(t.left||0),top:o.top-s.top-(t.top||0)}}const Un=()=>({left:window.scrollX,top:window.scrollY});function iu(e){let t;if("el"in e){const s=e.el,o=typeof s=="string"&&s.startsWith("#"),r=typeof s=="string"?o?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!r)return;t=ru(r,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function Cr(e,t){return(history.state?history.state.position-t:-1)+e}const fo=new Map;function lu(e,t){fo.set(e,t)}function au(e){const t=fo.get(e);return fo.delete(e),t}let cu=()=>location.protocol+"//"+location.host;function Xi(e,t){const{pathname:s,search:o,hash:r}=t,i=e.indexOf("#");if(i>-1){let c=r.includes(e.slice(i))?e.slice(i).length:1,a=r.slice(c);return a[0]!=="/"&&(a="/"+a),_r(a,"")}return _r(s,e)+o+r}function uu(e,t,s,o){let r=[],i=[],l=null;const c=({state:v})=>{const g=Xi(e,location),_=s.value,y=t.value;let w=0;if(v){if(s.value=g,t.value=v,l&&l===_){l=null;return}w=y?v.position-y.position:0}else o(g);r.forEach(T=>{T(s.value,_,{delta:w,type:Ys.pop,direction:w?w>0?Fs.forward:Fs.back:Fs.unknown})})};function a(){l=s.value}function u(v){r.push(v);const g=()=>{const _=r.indexOf(v);_>-1&&r.splice(_,1)};return i.push(g),g}function d(){const{history:v}=window;v.state&&v.replaceState(_e({},v.state,{scroll:Un()}),"")}function f(){for(const v of i)v();i=[],window.removeEventListener("popstate",c),window.removeEventListener("beforeunload",d)}return window.addEventListener("popstate",c),window.addEventListener("beforeunload",d,{passive:!0}),{pauseListeners:a,listen:u,destroy:f}}function xr(e,t,s,o=!1,r=!1){return{back:e,current:t,forward:s,replaced:o,position:window.history.length,scroll:r?Un():null}}function du(e){const{history:t,location:s}=window,o={value:Xi(e,s)},r={value:t.state};r.value||i(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function i(a,u,d){const f=e.indexOf("#"),v=f>-1?(s.host&&document.querySelector("base")?e:e.slice(f))+a:cu()+e+a;try{t[d?"replaceState":"pushState"](u,"",v),r.value=u}catch(g){console.error(g),s[d?"replace":"assign"](v)}}function l(a,u){const d=_e({},t.state,xr(r.value.back,a,r.value.forward,!0),u,{position:r.value.position});i(a,d,!0),o.value=a}function c(a,u){const d=_e({},r.value,t.state,{forward:a,scroll:Un()});i(d.current,d,!0);const f=_e({},xr(o.value,a,null),{position:d.position+1},u);i(a,f,!1),o.value=a}return{location:o,state:r,push:c,replace:l}}function fu(e){e=su(e);const t=du(e),s=uu(e,t.state,t.location,t.replace);function o(i,l=!0){l||s.pauseListeners(),history.go(i)}const r=_e({location:"",base:e,go:o,createHref:ou.bind(null,e)},t,s);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}function pu(e){return typeof e=="string"||e&&typeof e=="object"}function el(e){return typeof e=="string"||typeof e=="symbol"}const tl=Symbol("");var $r;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})($r||($r={}));function xs(e,t){return _e(new Error,{type:e,[tl]:!0},t)}function Et(e,t){return e instanceof Error&&tl in e&&(t==null||!!(e.type&t))}const Sr="[^/]+?",hu={sensitive:!1,strict:!1,start:!0,end:!0},vu=/[.+*?^${}()[\]/\\]/g;function gu(e,t){const s=_e({},hu,t),o=[];let r=s.start?"^":"";const i=[];for(const u of e){const d=u.length?[]:[90];s.strict&&!u.length&&(r+="/");for(let f=0;f<u.length;f++){const v=u[f];let g=40+(s.sensitive?.25:0);if(v.type===0)f||(r+="/"),r+=v.value.replace(vu,"\\$&"),g+=40;else if(v.type===1){const{value:_,repeatable:y,optional:w,regexp:T}=v;i.push({name:_,repeatable:y,optional:w});const A=T||Sr;if(A!==Sr){g+=10;try{new RegExp(`(${A})`)}catch(B){throw new Error(`Invalid custom RegExp for param "${_}" (${A}): `+B.message)}}let U=y?`((?:${A})(?:/(?:${A}))*)`:`(${A})`;f||(U=w&&u.length<2?`(?:/${U})`:"/"+U),w&&(U+="?"),r+=U,g+=20,w&&(g+=-8),y&&(g+=-20),A===".*"&&(g+=-50)}d.push(g)}o.push(d)}if(s.strict&&s.end){const u=o.length-1;o[u][o[u].length-1]+=.7000000000000001}s.strict||(r+="/?"),s.end?r+="$":s.strict&&!r.endsWith("/")&&(r+="(?:/|$)");const l=new RegExp(r,s.sensitive?"":"i");function c(u){const d=u.match(l),f={};if(!d)return null;for(let v=1;v<d.length;v++){const g=d[v]||"",_=i[v-1];f[_.name]=g&&_.repeatable?g.split("/"):g}return f}function a(u){let d="",f=!1;for(const v of e){(!f||!d.endsWith("/"))&&(d+="/"),f=!1;for(const g of v)if(g.type===0)d+=g.value;else if(g.type===1){const{value:_,repeatable:y,optional:w}=g,T=_ in u?u[_]:"";if(wt(T)&&!y)throw new Error(`Provided param "${_}" is an array but it is not repeatable (* or + modifiers)`);const A=wt(T)?T.join("/"):T;if(!A)if(w)v.length<2&&(d.endsWith("/")?d=d.slice(0,-1):f=!0);else throw new Error(`Missing required param "${_}"`);d+=A}}return d||"/"}return{re:l,score:o,keys:i,parse:c,stringify:a}}function mu(e,t){let s=0;for(;s<e.length&&s<t.length;){const o=t[s]-e[s];if(o)return o;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function sl(e,t){let s=0;const o=e.score,r=t.score;for(;s<o.length&&s<r.length;){const i=mu(o[s],r[s]);if(i)return i;s++}if(Math.abs(r.length-o.length)===1){if(Tr(o))return 1;if(Tr(r))return-1}return r.length-o.length}function Tr(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const wu={type:0,value:""},yu=/[a-zA-Z0-9_]/;function bu(e){if(!e)return[[]];if(e==="/")return[[wu]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(g){throw new Error(`ERR (${s})/"${u}": ${g}`)}let s=0,o=s;const r=[];let i;function l(){i&&r.push(i),i=[]}let c=0,a,u="",d="";function f(){u&&(s===0?i.push({type:0,value:u}):s===1||s===2||s===3?(i.length>1&&(a==="*"||a==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:u,regexp:d,repeatable:a==="*"||a==="+",optional:a==="*"||a==="?"})):t("Invalid state to consume buffer"),u="")}function v(){u+=a}for(;c<e.length;){if(a=e[c++],a==="\\"&&s!==2){o=s,s=4;continue}switch(s){case 0:a==="/"?(u&&f(),l()):a===":"?(f(),s=1):v();break;case 4:v(),s=o;break;case 1:a==="("?s=2:yu.test(a)?v():(f(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&c--);break;case 2:a===")"?d[d.length-1]=="\\"?d=d.slice(0,-1)+a:s=3:d+=a;break;case 3:f(),s=0,a!=="*"&&a!=="?"&&a!=="+"&&c--,d="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${u}"`),f(),l(),r}function _u(e,t,s){const o=gu(bu(e.path),s),r=_e(o,{record:e,parent:t,children:[],alias:[]});return t&&!r.record.aliasOf==!t.record.aliasOf&&t.children.push(r),r}function ku(e,t){const s=[],o=new Map;t=Mr({strict:!1,end:!0,sensitive:!1},t);function r(f){return o.get(f)}function i(f,v,g){const _=!g,y=Pr(f);y.aliasOf=g&&g.record;const w=Mr(t,f),T=[y];if("alias"in f){const B=typeof f.alias=="string"?[f.alias]:f.alias;for(const Z of B)T.push(Pr(_e({},y,{components:g?g.record.components:y.components,path:Z,aliasOf:g?g.record:y})))}let A,U;for(const B of T){const{path:Z}=B;if(v&&Z[0]!=="/"){const N=v.record.path,oe=N[N.length-1]==="/"?"":"/";B.path=v.record.path+(Z&&oe+Z)}if(A=_u(B,v,w),g?g.alias.push(A):(U=U||A,U!==A&&U.alias.push(A),_&&f.name&&!Ar(A)&&l(f.name)),nl(A)&&a(A),y.children){const N=y.children;for(let oe=0;oe<N.length;oe++)i(N[oe],A,g&&g.children[oe])}g=g||A}return U?()=>{l(U)}:Bs}function l(f){if(el(f)){const v=o.get(f);v&&(o.delete(f),s.splice(s.indexOf(v),1),v.children.forEach(l),v.alias.forEach(l))}else{const v=s.indexOf(f);v>-1&&(s.splice(v,1),f.record.name&&o.delete(f.record.name),f.children.forEach(l),f.alias.forEach(l))}}function c(){return s}function a(f){const v=$u(f,s);s.splice(v,0,f),f.record.name&&!Ar(f)&&o.set(f.record.name,f)}function u(f,v){let g,_={},y,w;if("name"in f&&f.name){if(g=o.get(f.name),!g)throw xs(1,{location:f});w=g.record.name,_=_e(Er(v.params,g.keys.filter(U=>!U.optional).concat(g.parent?g.parent.keys.filter(U=>U.optional):[]).map(U=>U.name)),f.params&&Er(f.params,g.keys.map(U=>U.name))),y=g.stringify(_)}else if(f.path!=null)y=f.path,g=s.find(U=>U.re.test(y)),g&&(_=g.parse(y),w=g.record.name);else{if(g=v.name?o.get(v.name):s.find(U=>U.re.test(v.path)),!g)throw xs(1,{location:f,currentLocation:v});w=g.record.name,_=_e({},v.params,f.params),y=g.stringify(_)}const T=[];let A=g;for(;A;)T.unshift(A.record),A=A.parent;return{name:w,path:y,params:_,matched:T,meta:xu(T)}}e.forEach(f=>i(f));function d(){s.length=0,o.clear()}return{addRoute:i,resolve:u,removeRoute:l,clearRoutes:d,getRoutes:c,getRecordMatcher:r}}function Er(e,t){const s={};for(const o of t)o in e&&(s[o]=e[o]);return s}function Pr(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Cu(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Cu(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const o in e.components)t[o]=typeof s=="object"?s[o]:s;return t}function Ar(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function xu(e){return e.reduce((t,s)=>_e(t,s.meta),{})}function Mr(e,t){const s={};for(const o in e)s[o]=o in t?t[o]:e[o];return s}function $u(e,t){let s=0,o=t.length;for(;s!==o;){const i=s+o>>1;sl(e,t[i])<0?o=i:s=i+1}const r=Su(e);return r&&(o=t.lastIndexOf(r,o-1)),o}function Su(e){let t=e;for(;t=t.parent;)if(nl(t)&&sl(e,t)===0)return t}function nl({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function Tu(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let r=0;r<o.length;++r){const i=o[r].replace(qi," "),l=i.indexOf("="),c=Js(l<0?i:i.slice(0,l)),a=l<0?null:Js(i.slice(l+1));if(c in t){let u=t[c];wt(u)||(u=t[c]=[u]),u.push(a)}else t[c]=a}return t}function Lr(e){let t="";for(let s in e){const o=e[s];if(s=Wc(s),o==null){o!==void 0&&(t+=(t.length?"&":"")+s);continue}(wt(o)?o.map(i=>i&&uo(i)):[o&&uo(o)]).forEach(i=>{i!==void 0&&(t+=(t.length?"&":"")+s,i!=null&&(t+="="+i))})}return t}function Eu(e){const t={};for(const s in e){const o=e[s];o!==void 0&&(t[s]=wt(o)?o.map(r=>r==null?null:""+r):o==null?o:""+o)}return t}const Pu=Symbol(""),Or=Symbol(""),Vn=Symbol(""),Io=Symbol(""),po=Symbol("");function Ms(){let e=[];function t(o){return e.push(o),()=>{const r=e.indexOf(o);r>-1&&e.splice(r,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Gt(e,t,s,o,r,i=l=>l()){const l=o&&(o.enterCallbacks[r]=o.enterCallbacks[r]||[]);return()=>new Promise((c,a)=>{const u=v=>{v===!1?a(xs(4,{from:s,to:t})):v instanceof Error?a(v):pu(v)?a(xs(2,{from:t,to:v})):(l&&o.enterCallbacks[r]===l&&typeof v=="function"&&l.push(v),c())},d=i(()=>e.call(o&&o.instances[r],t,s,u));let f=Promise.resolve(d);e.length<3&&(f=f.then(u)),f.catch(v=>a(v))})}function Yn(e,t,s,o,r=i=>i()){const i=[];for(const l of e)for(const c in l.components){let a=l.components[c];if(!(t!=="beforeRouteEnter"&&!l.instances[c]))if(Gi(a)){const d=(a.__vccOpts||a)[t];d&&i.push(Gt(d,s,o,l,c,r))}else{let u=a();i.push(()=>u.then(d=>{if(!d)throw new Error(`Couldn't resolve component "${c}" at "${l.path}"`);const f=Uc(d)?d.default:d;l.mods[c]=d,l.components[c]=f;const g=(f.__vccOpts||f)[t];return g&&Gt(g,s,o,l,c,r)()}))}}return i}function Rr(e){const t=gt(Vn),s=gt(Io),o=Te(()=>{const a=fe(e.to);return t.resolve(a)}),r=Te(()=>{const{matched:a}=o.value,{length:u}=a,d=a[u-1],f=s.matched;if(!d||!f.length)return-1;const v=f.findIndex(Cs.bind(null,d));if(v>-1)return v;const g=Ir(a[u-2]);return u>1&&Ir(d)===g&&f[f.length-1].path!==g?f.findIndex(Cs.bind(null,a[u-2])):v}),i=Te(()=>r.value>-1&&Ru(s.params,o.value.params)),l=Te(()=>r.value>-1&&r.value===s.matched.length-1&&Qi(s.params,o.value.params));function c(a={}){if(Ou(a)){const u=t[fe(e.replace)?"replace":"push"](fe(e.to)).catch(Bs);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:o,href:Te(()=>o.value.href),isActive:i,isExactActive:l,navigate:c}}function Au(e){return e.length===1?e[0]:e}const Mu=Ie({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Rr,setup(e,{slots:t}){const s=Me(Rr(e)),{options:o}=gt(Vn),r=Te(()=>({[Ur(e.activeClass,o.linkActiveClass,"router-link-active")]:s.isActive,[Ur(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const i=t.default&&Au(t.default(s));return e.custom?i:Hi("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:r.value},i)}}}),Lu=Mu;function Ou(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Ru(e,t){for(const s in t){const o=t[s],r=e[s];if(typeof o=="string"){if(o!==r)return!1}else if(!wt(r)||r.length!==o.length||o.some((i,l)=>i!==r[l]))return!1}return!0}function Ir(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Ur=(e,t,s)=>e??t??s,Iu=Ie({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const o=gt(po),r=Te(()=>e.route||o.value),i=gt(Or,0),l=Te(()=>{let u=fe(i);const{matched:d}=r.value;let f;for(;(f=d[u])&&!f.components;)u++;return u}),c=Te(()=>r.value.matched[l.value]);fn(Or,Te(()=>l.value+1)),fn(Pu,c),fn(po,r);const a=j();return Ns(()=>[a.value,c.value,e.name],([u,d,f],[v,g,_])=>{d&&(d.instances[f]=u,g&&g!==d&&u&&u===v&&(d.leaveGuards.size||(d.leaveGuards=g.leaveGuards),d.updateGuards.size||(d.updateGuards=g.updateGuards))),u&&d&&(!g||!Cs(d,g)||!v)&&(d.enterCallbacks[f]||[]).forEach(y=>y(u))},{flush:"post"}),()=>{const u=r.value,d=e.name,f=c.value,v=f&&f.components[d];if(!v)return Vr(s.default,{Component:v,route:u});const g=f.props[d],_=g?g===!0?u.params:typeof g=="function"?g(u):g:null,w=Hi(v,_e({},_,t,{onVnodeUnmounted:T=>{T.component.isUnmounted&&(f.instances[d]=null)},ref:a}));return Vr(s.default,{Component:w,route:u})||w}}});function Vr(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const Uu=Iu;function Vu(e){const t=ku(e.routes,e),s=e.parseQuery||Tu,o=e.stringifyQuery||Lr,r=e.history,i=Ms(),l=Ms(),c=Ms(),a=Vl(Ft);let u=Ft;ps&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const d=Zn.bind(null,M=>""+M),f=Zn.bind(null,Zc),v=Zn.bind(null,Js);function g(M,q){let G,Y;return el(M)?(G=t.getRecordMatcher(M),Y=q):Y=M,t.addRoute(Y,G)}function _(M){const q=t.getRecordMatcher(M);q&&t.removeRoute(q)}function y(){return t.getRoutes().map(M=>M.record)}function w(M){return!!t.getRecordMatcher(M)}function T(M,q){if(q=_e({},q||a.value),typeof M=="string"){const x=Jn(s,M,q.path),L=t.resolve({path:x.path},q),R=r.createHref(x.fullPath);return _e(x,L,{params:v(L.params),hash:Js(x.hash),redirectedFrom:void 0,href:R})}let G;if(M.path!=null)G=_e({},M,{path:Jn(s,M.path,q.path).path});else{const x=_e({},M.params);for(const L in x)x[L]==null&&delete x[L];G=_e({},M,{params:f(x)}),q.params=f(q.params)}const Y=t.resolve(G,q),ve=M.hash||"";Y.params=d(v(Y.params));const p=Qc(o,_e({},M,{hash:Gc(ve),path:Y.path})),h=r.createHref(p);return _e({fullPath:p,hash:ve,query:o===Lr?Eu(M.query):M.query||{}},Y,{redirectedFrom:void 0,href:h})}function A(M){return typeof M=="string"?Jn(s,M,a.value.path):_e({},M)}function U(M,q){if(u!==M)return xs(8,{from:q,to:M})}function B(M){return oe(M)}function Z(M){return B(_e(A(M),{replace:!0}))}function N(M){const q=M.matched[M.matched.length-1];if(q&&q.redirect){const{redirect:G}=q;let Y=typeof G=="function"?G(M):G;return typeof Y=="string"&&(Y=Y.includes("?")||Y.includes("#")?Y=A(Y):{path:Y},Y.params={}),_e({query:M.query,hash:M.hash,params:Y.path!=null?{}:M.params},Y)}}function oe(M,q){const G=u=T(M),Y=a.value,ve=M.state,p=M.force,h=M.replace===!0,x=N(G);if(x)return oe(_e(A(x),{state:typeof x=="object"?_e({},ve,x.state):ve,force:p,replace:h}),q||G);const L=G;L.redirectedFrom=q;let R;return!p&&Xc(o,Y,G)&&(R=xs(16,{to:L,from:Y}),et(Y,Y,!0,!1)),(R?Promise.resolve(R):z(L,Y)).catch(O=>Et(O)?Et(O,2)?O:ot(O):ge(O,L,Y)).then(O=>{if(O){if(Et(O,2))return oe(_e({replace:h},A(O.to),{state:typeof O.to=="object"?_e({},ve,O.to.state):ve,force:p}),q||L)}else O=H(L,Y,!0,h,ve);return se(L,Y,O),O})}function I(M,q){const G=U(M,q);return G?Promise.reject(G):Promise.resolve()}function k(M){const q=Bt.values().next().value;return q&&typeof q.runWithContext=="function"?q.runWithContext(M):M()}function z(M,q){let G;const[Y,ve,p]=ju(M,q);G=Yn(Y.reverse(),"beforeRouteLeave",M,q);for(const x of Y)x.leaveGuards.forEach(L=>{G.push(Gt(L,M,q))});const h=I.bind(null,M,q);return G.push(h),tt(G).then(()=>{G=[];for(const x of i.list())G.push(Gt(x,M,q));return G.push(h),tt(G)}).then(()=>{G=Yn(ve,"beforeRouteUpdate",M,q);for(const x of ve)x.updateGuards.forEach(L=>{G.push(Gt(L,M,q))});return G.push(h),tt(G)}).then(()=>{G=[];for(const x of p)if(x.beforeEnter)if(wt(x.beforeEnter))for(const L of x.beforeEnter)G.push(Gt(L,M,q));else G.push(Gt(x.beforeEnter,M,q));return G.push(h),tt(G)}).then(()=>(M.matched.forEach(x=>x.enterCallbacks={}),G=Yn(p,"beforeRouteEnter",M,q,k),G.push(h),tt(G))).then(()=>{G=[];for(const x of l.list())G.push(Gt(x,M,q));return G.push(h),tt(G)}).catch(x=>Et(x,8)?x:Promise.reject(x))}function se(M,q,G){c.list().forEach(Y=>k(()=>Y(M,q,G)))}function H(M,q,G,Y,ve){const p=U(M,q);if(p)return p;const h=q===Ft,x=ps?history.state:{};G&&(Y||h?r.replace(M.fullPath,_e({scroll:h&&x&&x.scroll},ve)):r.push(M.fullPath,ve)),a.value=M,et(M,q,G,h),ot()}let P;function le(){P||(P=r.listen((M,q,G)=>{if(!cs.listening)return;const Y=T(M),ve=N(Y);if(ve){oe(_e(ve,{replace:!0,force:!0}),Y).catch(Bs);return}u=Y;const p=a.value;ps&&lu(Cr(p.fullPath,G.delta),Un()),z(Y,p).catch(h=>Et(h,12)?h:Et(h,2)?(oe(_e(A(h.to),{force:!0}),Y).then(x=>{Et(x,20)&&!G.delta&&G.type===Ys.pop&&r.go(-1,!1)}).catch(Bs),Promise.reject()):(G.delta&&r.go(-G.delta,!1),ge(h,Y,p))).then(h=>{h=h||H(Y,p,!1),h&&(G.delta&&!Et(h,8)?r.go(-G.delta,!1):G.type===Ys.pop&&Et(h,20)&&r.go(-1,!1)),se(Y,p,h)}).catch(Bs)}))}let Oe=Ms(),Ae=Ms(),be;function ge(M,q,G){ot(M);const Y=Ae.list();return Y.length?Y.forEach(ve=>ve(M,q,G)):console.error(M),Promise.reject(M)}function nt(){return be&&a.value!==Ft?Promise.resolve():new Promise((M,q)=>{Oe.add([M,q])})}function ot(M){return be||(be=!M,le(),Oe.list().forEach(([q,G])=>M?G(M):q()),Oe.reset()),M}function et(M,q,G,Y){const{scrollBehavior:ve}=e;if(!ps||!ve)return Promise.resolve();const p=!G&&au(Cr(M.fullPath,0))||(Y||!G)&&history.state&&history.state.scroll||null;return To().then(()=>ve(M,q,p)).then(h=>h&&iu(h)).catch(h=>ge(h,M,q))}const He=M=>r.go(M);let Dt;const Bt=new Set,cs={currentRoute:a,listening:!0,addRoute:g,removeRoute:_,clearRoutes:t.clearRoutes,hasRoute:w,getRoutes:y,resolve:T,options:e,push:B,replace:Z,go:He,back:()=>He(-1),forward:()=>He(1),beforeEach:i.add,beforeResolve:l.add,afterEach:c.add,onError:Ae.add,isReady:nt,install(M){const q=this;M.component("RouterLink",Lu),M.component("RouterView",Uu),M.config.globalProperties.$router=q,Object.defineProperty(M.config.globalProperties,"$route",{enumerable:!0,get:()=>fe(a)}),ps&&!Dt&&a.value===Ft&&(Dt=!0,B(r.location).catch(ve=>{}));const G={};for(const ve in Ft)Object.defineProperty(G,ve,{get:()=>a.value[ve],enumerable:!0});M.provide(Vn,q),M.provide(Io,ii(G)),M.provide(po,a);const Y=M.unmount;Bt.add(M),M.unmount=function(){Bt.delete(M),Bt.size<1&&(u=Ft,P&&P(),P=null,a.value=Ft,Dt=!1,be=!1),Y()}}};function tt(M){return M.reduce((q,G)=>q.then(()=>k(G)),Promise.resolve())}return cs}function ju(e,t){const s=[],o=[],r=[],i=Math.max(t.matched.length,e.matched.length);for(let l=0;l<i;l++){const c=t.matched[l];c&&(e.matched.find(u=>Cs(u,c))?o.push(c):s.push(c));const a=e.matched[l];a&&(t.matched.find(u=>Cs(u,a))||r.push(a))}return[s,o,r]}function lt(){return gt(Vn)}function Nu(e){return gt(Io)}const ht="auth_token",Ht="user_info";class rt{static setToken(t,s=!1){s?(localStorage.setItem(ht,t),sessionStorage.removeItem(ht)):(sessionStorage.setItem(ht,t),localStorage.removeItem(ht))}static getToken(){const t=sessionStorage.getItem(ht);return t||localStorage.getItem(ht)}static removeToken(){localStorage.removeItem(ht),sessionStorage.removeItem(ht)}static hasToken(){return!!this.getToken()}static getAuthHeader(){const t=this.getToken();return t?`Bearer ${t}`:null}}class je{static setUser(t,s=!1){const o=JSON.stringify(t);s?(localStorage.setItem(Ht,o),sessionStorage.removeItem(Ht)):(sessionStorage.setItem(Ht,o),localStorage.removeItem(Ht))}static getUser(){let t=sessionStorage.getItem(Ht);if(t||(t=localStorage.getItem(Ht)),t)try{return JSON.parse(t)}catch{return this.removeUser(),null}return null}static removeUser(){localStorage.removeItem(Ht),sessionStorage.removeItem(Ht)}static isLoggedIn(){return!!this.getUser()&&rt.hasToken()}static getUserType(){return this.getUser()?.userType||null}static isAdmin(){return this.getUserType()==="ADMIN"}static isUser(){return this.getUserType()==="USER"}}class Ce{static login(t,s,o=!1){rt.setToken(t,o),je.setUser(s,o)}static logout(){rt.removeToken(),je.removeUser()}static isAuthenticated(){return je.isLoggedIn()}static getCurrentUser(){return je.getUser()}static getAuthHeader(){return rt.getAuthHeader()}static isRemembered(){return!!localStorage.getItem(ht)}static getTokenStorageType(){const t=sessionStorage.getItem(ht),s=localStorage.getItem(ht);return t?"session":s?"persistent":null}static initializeAuth(){const t=rt.getToken();if(t)try{const s=t.split(".");if(s.length!==3){this.logout();return}const o=JSON.parse(atob(s[1])),r=Math.floor(Date.now()/1e3);if(o.exp&&o.exp<r){this.logout();return}}catch{this.logout()}}}function Du(){return async function(t,s={}){const o=Ce.getAuthHeader(),r={...s.headers};s.body instanceof FormData||(r["Content-Type"]="application/json"),o&&(r.Authorization=o);try{const i=await fetch(t,{...s,headers:r});return i.status===401?(Ce.logout(),window.location.href="/login",i):i.status===403?(window.location.href="/error/403",i):(i.status>=500&&(window.location.href="/error/500"),i)}catch(i){throw i instanceof TypeError&&i.message.includes("fetch")&&(window.location.href="/error/network"),i}}}const Ts=Du();async function Bu(e){if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);const t=await e.json();if(!t.success)throw new Error(t.message||"API request failed");return t.data}async function Fu(e){if(!e.ok)throw new Error(`HTTP error! status: ${e.status}`);return await e.json()}async function jn(e,t={}){const s=await Ts(e,t);return Bu(s)}async function Hu(e,t={}){const s=await Ts(e,t);return Fu(s)}const os=j(null),zu=Te(()=>!!os.value),ho=()=>{const e=je.getUser();e&&Ce.isAuthenticated()?os.value=e:os.value=null};ho();function Nt(){const e=a=>{os.value=a,je.setUser(a)},t=()=>{ho()},s=()=>{os.value=null,Ce.logout()},o=()=>{t()},r=()=>{os.value=null,Ce.logout(),ho()},i=Te(()=>je.isAdmin()),l=Te(()=>je.isUser()),c=Te(()=>je.getUserType());return{user:Te(()=>os.value),isLoggedIn:zu,isAdmin:i,isUser:l,userType:c,setUser:e,clearUser:s,initUser:o,logout:r,updateUserState:t}}const Ku={name:"AvatarDisplay",props:{avatarUrl:{type:String,default:null},name:{type:String,default:""},size:{type:Number,default:40},clickable:{type:Boolean,default:!1},showUploadOverlay:{type:Boolean,default:!1},showStatus:{type:Boolean,default:!1},status:{type:String,default:"offline"}},data(){return{showPreview:!1,hoverTimer:null,previewPosition:{x:0,y:0},clickDisableTimer:null,isClickDisabled:!1}},computed:{avatarLetter(){return this.name?/[\u4e00-\u9fa5]/.test(this.name)?this.name.charAt(this.name.length-1):this.name.charAt(0).toUpperCase():"?"},letterAvatarColor(){const e=["#FF6B6B","#4ECDC4","#45B7D1","#96CEB4","#FFEAA7","#DDA0DD","#98D8C8","#F7DC6F","#BB8FCE","#85C1E9"];if(!this.name)return e[0];let t=0;for(let s=0;s<this.name.length;s++)t=this.name.charCodeAt(s)+((t<<5)-t);return e[Math.abs(t)%e.length]},letterFontSize(){return Math.floor(this.size*.4)},statusClass(){return`status-${this.status}`},previewStyle(){return{left:this.previewPosition.x+"px",top:this.previewPosition.y+"px"}}},methods:{handleClick(e){this.clearHoverTimer(),this.showPreview=!1,this.isClickDisabled=!0,this.clickDisableTimer&&clearTimeout(this.clickDisableTimer),this.clickDisableTimer=setTimeout(()=>{this.isClickDisabled=!1},2e3),this.clickable&&(e&&e.stopPropagation&&e.stopPropagation(),this.$emit("click"))},handleImageError(){this.$emit("image-error")},handleMouseEnter(e){if(!this.avatarUrl||this.isClickDisabled)return;this.clearHoverTimer();const t=e.currentTarget;this.hoverTimer=setTimeout(()=>{t&&t.isConnected&&this.showPreview!==!0&&!this.isClickDisabled&&(this.calculatePreviewPosition(t),this.showPreview=!0)},1e3)},handleMouseLeave(){this.clearHoverTimer(),this.showPreview=!1},calculatePreviewPosition(e){try{if(!e||!e.isConnected){console.warn("Element not available for preview positioning");return}const t=e.getBoundingClientRect();if(!t||t.width===0||t.height===0){console.warn("Invalid element bounds for preview positioning");return}const s=300,o=20;let r=t.right+o,i=t.top;r+s>window.innerWidth&&(r=t.left-s-o),i+s>window.innerHeight&&(i=window.innerHeight-s-o),r=Math.max(o,r),i=Math.max(o,i),this.previewPosition={x:r,y:i}}catch(t){console.error("Error calculating preview position:",t),this.showPreview=!1}},handlePreviewLoad(){},handlePreviewError(){this.showPreview=!1},clearHoverTimer(){this.hoverTimer&&(clearTimeout(this.hoverTimer),this.hoverTimer=null)},clearClickDisableTimer(){this.clickDisableTimer&&(clearTimeout(this.clickDisableTimer),this.clickDisableTimer=null)},cleanup(){this.clearHoverTimer(),this.clearClickDisableTimer(),this.showPreview=!1,this.isClickDisabled=!1}},beforeUnmount(){this.cleanup()},beforeDestroy(){this.cleanup()}},Gu=["src","alt"],Wu={key:2,class:"upload-overlay"},qu=["src","alt"];function Zu(e,t,s,o,r,i){return b(),C(ae,null,[n("div",{class:pe(["avatar-display",{clickable:s.clickable}]),onClick:t[1]||(t[1]=(...l)=>i.handleClick&&i.handleClick(...l)),onMouseenter:t[2]||(t[2]=(...l)=>i.handleMouseEnter&&i.handleMouseEnter(...l)),onMouseleave:t[3]||(t[3]=(...l)=>i.handleMouseLeave&&i.handleMouseLeave(...l))},[n("div",{class:"avatar-container",style:Ot({width:s.size+"px",height:s.size+"px"})},[s.avatarUrl?(b(),C("img",{key:0,src:s.avatarUrl,alt:s.name||"Avatar",class:"avatar-image",onError:t[0]||(t[0]=(...l)=>i.handleImageError&&i.handleImageError(...l))},null,40,Gu)):(b(),C("div",{key:1,class:"avatar-letter",style:Ot({backgroundColor:i.letterAvatarColor,fontSize:i.letterFontSize+"px",lineHeight:s.size+"px"})},S(i.avatarLetter),5)),s.showUploadOverlay?(b(),C("div",Wu,t[6]||(t[6]=[n("i",{class:"upload-icon"},"📷",-1)]))):J("",!0)],4),s.showStatus?(b(),C("div",{key:0,class:pe(["status-indicator",i.statusClass])},null,2)):J("",!0)],34),(b(),Xe(Zl,{to:"body"},[r.showPreview&&s.avatarUrl?(b(),C("div",{key:0,class:"avatar-preview",style:Ot(i.previewStyle)},[n("img",{src:s.avatarUrl,alt:s.name||"Avatar Preview",class:"preview-image",onLoad:t[4]||(t[4]=(...l)=>i.handlePreviewLoad&&i.handlePreviewLoad(...l)),onError:t[5]||(t[5]=(...l)=>i.handlePreviewError&&i.handlePreviewError(...l))},null,40,qu)],4)):J("",!0)]))],64)}const Uo=Ue(Ku,[["render",Zu],["__scopeId","data-v-b84dbf16"]]),is={BASE_URL:"http://localhost:8080",UPLOAD:{MAX_SIZE:10*1024*1024,ALLOWED_TYPES:["image/jpeg","image/jpg","image/png","image/gif"],ALLOWED_EXTENSIONS:["jpg","jpeg","png","gif"]}};function Vo(e){if(!e)return null;if(e.startsWith("http://")||e.startsWith("https://"))return e;if(e.startsWith("/api/files/"))return`${is.BASE_URL}${e}`;if(e.includes("\\uploads\\avatars\\")){const t=e.split("\\").pop();return`${is.BASE_URL}/api/files/uploads/avatars/${t}`}return`${is.BASE_URL}/api/files/${e}`}function Ju(e){return is.UPLOAD.ALLOWED_TYPES.includes(e.type)}function Yu(e){return e.size<=is.UPLOAD.MAX_SIZE}function Qu(e){const s=["Bytes","KB","MB","GB"],o=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,o)).toFixed(2))+" "+s[o]}function Xu(){return Qu(is.UPLOAD.MAX_SIZE)}function ed(){return is.UPLOAD.ALLOWED_EXTENSIONS.map(e=>e.toUpperCase()).join("、")}const td={class:"top-navbar"},sd={class:"nav-content"},nd={class:"nav-brand"},od={key:0,class:"page-title"},rd={class:"nav-actions"},id={class:"welcome-text"},ld=Ie({__name:"TopNavbar",props:{pageTitle:{},backTo:{},backText:{},showUserInfo:{type:Boolean,default:!0},showAvatar:{type:Boolean,default:!1},showDashboardLink:{type:Boolean,default:!1},showLogoutButton:{type:Boolean,default:!1}},emits:["avatar-click","logout"],setup(e,{emit:t}){const{user:s,isLoggedIn:o}=Nt();return(r,i)=>{const l=ft("router-link");return b(),C("nav",td,[n("div",sd,[n("div",nd,[r.backTo?(b(),Xe(l,{key:0,to:r.backTo,class:"back-link"},{default:me(()=>[i[2]||(i[2]=n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),ee(" "+S(r.backText||"返回"),1)]),_:1,__:[2]},8,["to"])):(b(),C(ae,{key:1},[i[3]||(i[3]=Le('<div class="brand-logo" data-v-5572c81c><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-5572c81c><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-5572c81c></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-5572c81c></circle></svg></div><span class="brand-name" data-v-5572c81c>膳食营养分析平台</span>',2))],64))]),r.pageTitle?(b(),C("h1",od,S(r.pageTitle),1)):J("",!0),n("div",rd,[ua(r.$slots,"actions",{},void 0),r.showUserInfo&&fe(o)?(b(),C(ae,{key:0},[n("span",id,"欢迎，"+S(fe(s)?.username),1),r.showAvatar?(b(),C("div",{key:0,class:"user-avatar",onClick:i[0]||(i[0]=c=>r.$emit("avatar-click"))},[ue(Uo,{"avatar-url":fe(Vo)(fe(s)?.avatar),name:fe(s)?.username,size:36,clickable:!1,"show-upload-overlay":!0},null,8,["avatar-url","name"])])):J("",!0),r.showDashboardLink?(b(),Xe(l,{key:1,to:fe(s)?.userType==="ADMIN"?"/admin/dashboard":"/dashboard",class:"nav-btn dashboard-btn"},{default:me(()=>[ee(S(fe(s)?.userType==="ADMIN"?"管理后台":"控制台"),1)]),_:1},8,["to"])):J("",!0),r.showLogoutButton?(b(),C("button",{key:2,onClick:i[1]||(i[1]=c=>r.$emit("logout")),class:"nav-btn logout-btn"}," 退出登录 ")):J("",!0)],64)):r.showUserInfo&&!fe(o)?(b(),C(ae,{key:1},[ue(l,{to:"/login",class:"nav-btn login-btn"},{default:me(()=>i[4]||(i[4]=[ee("登录",-1)])),_:1,__:[4]}),ue(l,{to:"/register",class:"nav-btn register-btn"},{default:me(()=>i[5]||(i[5]=[ee("注册",-1)])),_:1,__:[5]})],64)):J("",!0)])])])}}}),tn=Ue(ld,[["__scopeId","data-v-5572c81c"]]),Es="http://localhost:8080/api/core-features";async function sn(e,t={}){return(await Ts(e,t)).json()}async function ad(){return(await fetch(`${Es}/enabled`)).json()}async function cd(e){return sn(`${Es}/admin/create`,{method:"POST",body:JSON.stringify(e)})}async function ud(e,t){return sn(`${Es}/admin/${e}`,{method:"PUT",body:JSON.stringify(t)})}async function dd(e){return sn(`${Es}/admin/${e}`,{method:"DELETE"})}async function fd(e){const t=new URLSearchParams;return e.page&&t.append("page",e.page.toString()),e.size&&t.append("size",e.size.toString()),e.title&&t.append("title",e.title),e.status!==void 0&&t.append("status",e.status.toString()),sn(`${Es}/admin/list?${t.toString()}`)}async function pd(e,t){return sn(`${Es}/admin/${e}/status?status=${t}`,{method:"PUT"})}const hd={class:"home-container"},vd={class:"hero-section"},gd={class:"hero-content"},md={class:"hero-actions"},wd={class:"section-content"},yd={key:0,class:"loading-container"},bd={key:1,class:"features-grid"},_d=["onClick"],kd=["innerHTML"],Cd={class:"feature-title"},xd={class:"feature-description"},$d={class:"feature-highlights"},Sd={class:"footer-section"},Td={class:"footer-content"},Ed={class:"footer-info"},Pd={key:0,class:"admin-link"},Ad=Ie({__name:"HomeView",setup(e){const t=lt(),{user:s,isLoggedIn:o,initUser:r,logout:i,updateUserState:l}=Nt(),c=j(),a=j([]),u=j(!1),d=async()=>{u.value=!0;try{const _=await ad();_.success&&_.data&&(a.value=_.data)}catch{}finally{u.value=!1}},f=()=>{c.value?.scrollIntoView({behavior:"smooth"})},v=_=>{if(!o.value){t.push("/login");return}},g=()=>{i(),l()};return jt(()=>{r(),d()}),(_,y)=>{const w=ft("router-link");return b(),C("div",hd,[ue(tn,{"show-dashboard-link":!0,"show-logout-button":!0,onLogout:g}),y[10]||(y[10]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),n("section",vd,[n("div",gd,[y[3]||(y[3]=Le('<h1 class="hero-title" data-v-8b16026c>科学饮食，智慧健康</h1><p class="hero-subtitle" data-v-8b16026c> 基于AI技术的个性化营养分析平台，为您提供专业的膳食指导和健康管理服务 </p><div class="hero-features" data-v-8b16026c><div class="feature-tag" data-v-8b16026c>🥗 智能营养分析</div><div class="feature-tag" data-v-8b16026c>📊 个性化建议</div><div class="feature-tag" data-v-8b16026c>🎯 健康目标管理</div></div>',3)),n("div",md,[fe(o)?fe(s)?.userType==="USER"?(b(),Xe(w,{key:1,to:"/dashboard",class:"cta-btn primary"},{default:me(()=>y[1]||(y[1]=[ee("进入控制台",-1)])),_:1,__:[1]})):fe(s)?.userType==="ADMIN"?(b(),Xe(w,{key:2,to:"/admin/dashboard",class:"cta-btn primary"},{default:me(()=>y[2]||(y[2]=[ee("管理后台",-1)])),_:1,__:[2]})):J("",!0):(b(),Xe(w,{key:0,to:"/login",class:"cta-btn primary"},{default:me(()=>y[0]||(y[0]=[ee("立即开始",-1)])),_:1,__:[0]})),n("button",{class:"cta-btn secondary",onClick:f},"了解更多")])])]),n("section",{class:"features-section",ref_key:"featuresSection",ref:c},[n("div",wd,[y[5]||(y[5]=n("div",{class:"section-header"},[n("h2",null,"八大核心功能"),n("p",null,"全方位的营养健康管理服务，从基础记录到智能分析")],-1)),u.value?(b(),C("div",yd,y[4]||(y[4]=[n("div",{class:"loading-spinner"},null,-1),n("p",null,"正在加载核心功能...",-1)]))):(b(),C("div",bd,[(b(!0),C(ae,null,Ve(a.value,T=>(b(),C("div",{key:T.id,class:"feature-card",onClick:A=>v()},[n("div",{class:"feature-icon",innerHTML:T.icon},null,8,kd),n("h3",Cd,S(T.title),1),n("p",xd,S(T.description),1),n("div",$d,[(b(!0),C(ae,null,Ve(T.highlights,A=>(b(),C("span",{key:A,class:"highlight-tag"},S(A),1))),128))])],8,_d))),128))]))])],512),n("footer",Sd,[n("div",Td,[y[9]||(y[9]=Le('<div class="footer-brand" data-v-8b16026c><div class="brand-logo" data-v-8b16026c><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-8b16026c><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-8b16026c></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-8b16026c></circle></svg></div><span class="brand-name" data-v-8b16026c>膳食营养分析平台</span></div>',1)),n("div",Ed,[y[7]||(y[7]=n("p",null,"© 2025 膳食营养分析平台. 智能营养管理服务",-1)),y[8]||(y[8]=n("p",null,"科学饮食，健康生活",-1)),fe(o)?J("",!0):(b(),C("div",Pd,[ue(w,{to:"/admin/login",class:"admin-login-link"},{default:me(()=>y[6]||(y[6]=[ee("管理员登录",-1)])),_:1,__:[6]})]))])])])])}}}),Md=Ue(Ad,[["__scopeId","data-v-8b16026c"]]);function Ld(e,t,s){Ce.isAuthenticated()?je.isAdmin()?s():s("/error/403"):s({path:"/admin/login",query:{redirect:e.fullPath}})}function cn(e,t,s){if(Ce.isAuthenticated()){const o=je.getUserType();s(o==="ADMIN"?"/admin/dashboard":"/")}else s()}function ts(e){return(t,s,o)=>{if(!Ce.isAuthenticated())o({path:"/login",query:{redirect:t.fullPath}});else{const r=je.getUserType();r&&e.includes(r)?o():o("/error/403")}}}function jr(e,t,s){Ce.isAuthenticated()&&je.isAdmin()?s("/admin/dashboard"):s()}const Yt="http://localhost:8080/api/user";async function Od(e){return(await fetch(`${Yt}/register`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json()}async function Rd(e){const s=await(await fetch(`${Yt}/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:o,...r}=s.data;o&&Ce.login(o,{...r,userType:"USER"})}return s}async function Id(e){const s=await(await fetch(`${Yt}/login-with-code`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:o,...r}=s.data;o&&Ce.login(o,{...r,userType:"USER"})}return s}async function $n(e,t="REGISTER"){return(await fetch(`${Yt}/send-verification-code`,{method:"POST",headers:{"Content-Type":"application/x-www-form-urlencoded"},body:`email=${encodeURIComponent(e)}&type=${encodeURIComponent(t)}`})).json()}async function Ud(e){return(await fetch(`${Yt}/reset-password`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json()}async function ol(e){const t=new FormData;return t.append("file",e),(await Ts(`${Yt}/api/files/avatar/user`,{method:"POST",body:t})).json()}async function Vd(){return await(await Ts(`${Yt}/profile`)).json()}async function jd(e){return Hu(`${Yt}/profile`,{method:"PUT",body:JSON.stringify(e)})}const Nd={class:"login-container"},Dd={class:"login-content"},Bd={class:"form-section"},Fd={class:"form-container"},Hd={key:0,class:"success-message"},zd={class:"success-content"},Kd={key:1,class:"global-error-message"},Gd={class:"error-content"},Wd={class:"login-mode-switch"},qd={class:"form-group"},Zd={class:"input-wrapper"},Jd={key:0,class:"error-message"},Yd={class:"form-group"},Qd={class:"input-wrapper"},Xd=["type"],ef={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},tf={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},sf={key:0,class:"error-message"},nf={class:"form-group"},of={class:"input-wrapper"},rf={key:0,class:"error-message"},lf={class:"form-group"},af={class:"verification-input-wrapper"},cf=["disabled"],uf={key:0,class:"loading-spinner"},df={key:1},ff={key:2},pf={key:0,class:"error-message"},hf={class:"form-options"},vf={class:"checkbox-wrapper",title:"勾选后，关闭浏览器仍保持登录状态30天"},gf=["disabled"],mf={key:0,class:"loading-spinner"},wf={class:"form-footer"},yf=Ie({__name:"LoginView",setup(e){const t=lt(),s=Nu(),{updateUserState:o}=Nt(),r=j(!1),i=j(!1),l=j(""),c=j(!1),a=j(!1),u=j(""),d=j("password"),f=j(!1),v=j(0),g=j(null),_=Me({emailOrPhone:"",password:""}),y=Me({email:"",verificationCode:""}),w=Me({}),T=()=>{const I=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;_.emailOrPhone?I.test(_.emailOrPhone)?w.emailOrPhone=void 0:w.emailOrPhone="请输入有效的邮箱地址":w.emailOrPhone="请输入邮箱地址"},A=()=>{_.password?_.password.length<6?w.password="密码长度至少6位":w.password=void 0:w.password="请输入密码"},U=()=>{const I=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;y.email?I.test(y.email)?w.email=void 0:w.email="请输入有效的邮箱地址":w.email="请输入邮箱地址"},B=()=>{y.verificationCode?y.verificationCode.length!==6?w.verificationCode="验证码为6位数字":w.verificationCode=void 0:w.verificationCode="请输入验证码"},Z=async()=>{if(U(),!w.email){f.value=!0;try{const I=await $n(y.email,"LOGIN");I.success?(v.value=60,g.value=setInterval(()=>{v.value--,v.value<=0&&(clearInterval(g.value),g.value=null)},1e3)):u.value=I.message}catch(I){u.value=I.message||"发送验证码失败"}finally{f.value=!1}}},N=()=>{d.value=d.value==="password"?"code":"password",u.value="",Object.keys(w).forEach(I=>{delete w[I]})},oe=async()=>{if(u.value="",d.value==="password"){if(T(),A(),w.emailOrPhone||w.password)return}else if(U(),B(),w.email||w.verificationCode)return;a.value=!0;try{let I;if(d.value==="password"?I=await Rd({emailOrPhone:_.emailOrPhone,password:_.password,rememberMe:i.value}):I=await Id({email:y.email,verificationCode:y.verificationCode,rememberMe:i.value}),I.success&&I.data){const{token:k,...z}=I.data;k&&(Ce.login(k,{...z,userType:"USER"},i.value),o()),await new Promise(H=>setTimeout(H,200));const se=s.query.redirect;se?t.push(se):t.push("/")}else u.value=I.message}catch(I){u.value=I.message||"登录失败，请检查网络连接后重试"}finally{a.value=!1}};return jt(()=>{const I=s.query.message,k=s.query.type;I&&k==="success"&&(l.value=I,c.value=!0,setTimeout(()=>{c.value=!1},5e3),t.replace({path:"/login"}))}),Ss(()=>{g.value&&clearInterval(g.value)}),(I,k)=>{const z=ft("router-link");return b(),C("div",Nd,[k[25]||(k[25]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),n("div",Dd,[k[24]||(k[24]=Le('<div class="brand-section" data-v-5e618a97><div class="brand-logo" data-v-5e618a97><div class="logo-icon" data-v-5e618a97><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-5e618a97><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-5e618a97></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-5e618a97></circle></svg></div><h1 class="brand-title" data-v-5e618a97>膳食营养分析平台</h1></div><p class="brand-subtitle" data-v-5e618a97>科学饮食，健康生活</p><div class="feature-list" data-v-5e618a97><div class="feature-item" data-v-5e618a97><span class="feature-icon" data-v-5e618a97>🥗</span><span data-v-5e618a97>智能营养分析</span></div><div class="feature-item" data-v-5e618a97><span class="feature-icon" data-v-5e618a97>📊</span><span data-v-5e618a97>个性化建议</span></div><div class="feature-item" data-v-5e618a97><span class="feature-icon" data-v-5e618a97>🎯</span><span data-v-5e618a97>健康目标管理</span></div></div></div>',1)),n("div",Bd,[n("div",Fd,[k[23]||(k[23]=n("div",{class:"form-header"},[n("h2",null,"欢迎回来"),n("p",null,"登录您的账户，继续您的健康之旅")],-1)),c.value?(b(),C("div",Hd,[n("div",zd,[k[6]||(k[6]=n("svg",{class:"success-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),n("span",null,S(l.value),1)])])):J("",!0),u.value?(b(),C("div",Kd,[n("div",Gd,[k[7]||(k[7]=n("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),n("span",null,S(u.value),1)])])):J("",!0),n("div",Wd,[d.value==="code"?(b(),C("button",{key:0,type:"button",class:"mode-button",onClick:N},k[8]||(k[8]=[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M12 1L15.09 8.26L22 9L15.09 9.74L12 17L8.91 9.74L2 9L8.91 8.26L12 1Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 使用密码登录 ",-1)]))):J("",!0),d.value==="password"?(b(),C("button",{key:1,type:"button",class:"mode-button",onClick:N},k[9]||(k[9]=[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 使用验证码登录 ",-1)]))):J("",!0)]),n("form",{onSubmit:kt(oe,["prevent"]),class:"login-form"},[d.value==="password"?(b(),C(ae,{key:0},[n("div",qd,[k[11]||(k[11]=n("label",{for:"emailOrPhone",class:"form-label"},"邮箱地址",-1)),n("div",Zd,[te(n("input",{id:"emailOrPhone","onUpdate:modelValue":k[0]||(k[0]=se=>_.emailOrPhone=se),type:"text",class:pe(["form-input",{error:w.emailOrPhone}]),placeholder:"请输入邮箱地址",onBlur:T},null,34),[[de,_.emailOrPhone]]),k[10]||(k[10]=n("span",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),w.emailOrPhone?(b(),C("span",Jd,S(w.emailOrPhone),1)):J("",!0)]),n("div",Yd,[k[14]||(k[14]=n("label",{for:"password",class:"form-label"},"密码",-1)),n("div",Qd,[te(n("input",{id:"password","onUpdate:modelValue":k[1]||(k[1]=se=>_.password=se),type:r.value?"text":"password",class:pe(["form-input",{error:w.password}]),placeholder:"请输入您的密码",onBlur:A},null,42,Xd),[[ks,_.password]]),n("button",{type:"button",class:"password-toggle",onClick:k[2]||(k[2]=se=>r.value=!r.value)},[r.value?(b(),C("svg",ef,k[12]||(k[12]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",tf,k[13]||(k[13]=[n("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),w.password?(b(),C("span",sf,S(w.password),1)):J("",!0)])],64)):(b(),C(ae,{key:1},[n("div",nf,[k[16]||(k[16]=n("label",{for:"email",class:"form-label"},"邮箱地址",-1)),n("div",of,[te(n("input",{id:"email","onUpdate:modelValue":k[3]||(k[3]=se=>y.email=se),type:"email",class:pe(["form-input",{error:w.email}]),placeholder:"请输入邮箱地址",onBlur:U},null,34),[[de,y.email]]),k[15]||(k[15]=n("span",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),w.email?(b(),C("span",rf,S(w.email),1)):J("",!0)]),n("div",lf,[k[17]||(k[17]=n("label",{for:"verificationCode",class:"form-label"},"验证码",-1)),n("div",af,[te(n("input",{id:"verificationCode","onUpdate:modelValue":k[4]||(k[4]=se=>y.verificationCode=se),type:"text",class:pe(["form-input verification-input",{error:w.verificationCode}]),placeholder:"请输入6位验证码",maxlength:"6",onBlur:B},null,34),[[de,y.verificationCode]]),n("button",{type:"button",class:"send-code-button",disabled:f.value||v.value>0||!y.email,onClick:Z},[f.value?(b(),C("span",uf)):v.value>0?(b(),C("span",df,S(v.value)+"s",1)):(b(),C("span",ff,"发送验证码"))],8,cf)]),w.verificationCode?(b(),C("span",pf,S(w.verificationCode),1)):J("",!0)])],64)),n("div",hf,[n("label",vf,[te(n("input",{type:"checkbox","onUpdate:modelValue":k[5]||(k[5]=se=>i.value=se)},null,512),[[Zs,i.value]]),k[18]||(k[18]=n("span",{class:"checkmark"},null,-1)),k[19]||(k[19]=n("span",{class:"checkbox-label"},"记住我",-1))]),ue(z,{to:"/forgot-password",class:"forgot-password"},{default:me(()=>k[20]||(k[20]=[ee("忘记密码？",-1)])),_:1,__:[20]})]),n("button",{type:"submit",class:"login-button",disabled:a.value},[a.value?(b(),C("span",mf)):J("",!0),n("span",null,S(a.value?"登录中...":"登录"),1)],8,gf)],32),n("div",wf,[n("p",null,[k[22]||(k[22]=ee("还没有账户？ ",-1)),ue(z,{to:"/register",class:"register-link"},{default:me(()=>k[21]||(k[21]=[ee("立即注册",-1)])),_:1,__:[21]})])])])])])])}}}),bf=Ue(yf,[["__scopeId","data-v-5e618a97"]]),_f={class:"password-strength-meter"},kf={class:"strength-bar"},Cf={class:"strength-text"},xf={key:0,class:"suggestions"},$f=Ie({__name:"PasswordStrengthMeter",props:{password:{}},setup(e){const t=e,s=Te(()=>{const c=t.password;if(!c)return{score:0,text:"",suggestions:[]};let a=0;const u=[];c.length>=8?a+=1:u.push("密码长度至少8位"),c.length>=12?a+=1:c.length>=8&&u.push("建议密码长度12位以上"),/[a-z]/.test(c)?a+=1:u.push("包含小写字母"),/[A-Z]/.test(c)?a+=1:u.push("包含大写字母"),/[0-9]/.test(c)?a+=1:u.push("包含数字"),/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/.test(c)?a+=1:u.push("包含特殊字符"),["123456","password","123456789","12345678","12345","1234567","admin","123123","qwerty","abc123"].includes(c.toLowerCase())&&(a=0,u.push("避免使用常见密码")),/(.)\1{2,}/.test(c)&&(a-=1,u.push("避免连续重复字符")),a=Math.max(0,Math.min(6,a));let f="";return a===0?f="很弱":a<=2?f="弱":a<=4?f="中等":f="强",{score:a,text:f,suggestions:u}}),o=Te(()=>s.value.score/6*100),r=Te(()=>s.value.text),i=Te(()=>{const c=s.value.score;return c===0?"very-weak":c<=2?"weak":c<=4?"medium":"strong"}),l=Te(()=>s.value.suggestions);return(c,a)=>(b(),C("div",_f,[n("div",kf,[n("div",{class:pe(["strength-fill",i.value]),style:Ot({width:o.value+"%"})},null,6)]),n("div",Cf,[n("span",{class:pe(i.value)},S(r.value),3)]),l.value.length>0?(b(),C("div",xf,[a[0]||(a[0]=n("p",{class:"suggestions-title"},"密码建议：",-1)),n("ul",null,[(b(!0),C(ae,null,Ve(l.value,u=>(b(),C("li",{key:u},S(u),1))),128))])])):J("",!0)]))}}),Sf=Ue($f,[["__scopeId","data-v-3b5f4836"]]),Tf={class:"register-container"},Ef={class:"register-content"},Pf={class:"form-section"},Af={class:"form-container"},Mf={key:0,class:"success-message"},Lf={class:"success-content"},Of={key:1,class:"global-error-message"},Rf={class:"error-content"},If={class:"form-group"},Uf={class:"input-wrapper"},Vf={key:0,class:"error-message"},jf={class:"form-group"},Nf={class:"input-wrapper"},Df={key:0,class:"error-message"},Bf={class:"form-group"},Ff={class:"verification-input-wrapper"},Hf=["disabled"],zf={key:0,class:"error-message"},Kf={class:"form-group"},Gf={class:"input-wrapper"},Wf=["type"],qf={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Zf={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Jf={key:1,class:"error-message"},Yf={class:"form-group"},Qf={class:"input-wrapper"},Xf=["type"],e1={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},t1={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},s1={key:0,class:"error-message"},n1={class:"form-group"},o1={class:"checkbox-wrapper"},r1=["disabled"],i1={key:0,class:"loading-spinner"},l1={class:"form-footer"},a1=Ie({__name:"RegisterView",setup(e){const t=lt(),s=j(!1),o=j(!1),r=j(!1),i=j(!1),l=j(""),c=j(!1),a=j(""),u=Me({username:"",email:"",password:"",confirmPassword:"",verificationCode:""}),d=Me({}),f=j(!1),v=j(0),g=Te(()=>u.email&&!d.email&&v.value===0),_=Te(()=>{const I=u.password;if(!I)return{score:0,text:"",class:"",width:"0%"};let k=0;return I.length>=8&&(k+=1),I.length>=12&&(k+=1),/[a-z]/.test(I)&&(k+=1),/[A-Z]/.test(I)&&(k+=1),/[0-9]/.test(I)&&(k+=1),/[^A-Za-z0-9]/.test(I)&&(k+=1),k<=2?{score:k,text:"弱",class:"weak",width:"33%"}:k<=4?{score:k,text:"中等",class:"medium",width:"66%"}:{score:k,text:"强",class:"strong",width:"100%"}}),y=()=>{u.username?u.username.length<3?d.username="用户名至少3个字符":u.username.length>20?d.username="用户名不能超过20个字符":/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/.test(u.username)?d.username=void 0:d.username="用户名只能包含字母、数字、下划线和中文":d.username="请输入用户名"},w=()=>{const I=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;u.email?I.test(u.email)?d.email=void 0:d.email="请输入有效的邮箱地址":d.email="请输入邮箱地址"},T=()=>{u.password?u.password.length<8?d.password="密码长度至少8位":_.value.score<2?d.password="密码强度太弱，请包含字母、数字或特殊字符":d.password=void 0:d.password="请输入密码"},A=()=>{u.confirmPassword?u.password!==u.confirmPassword?d.confirmPassword="两次输入的密码不一致":d.confirmPassword=void 0:d.confirmPassword="请确认密码"},U=()=>{u.confirmPassword&&u.password!==u.confirmPassword?d.confirmPassword="两次输入的密码不一致":d.confirmPassword=void 0},B=()=>{u.verificationCode?u.verificationCode.length!==6?d.verificationCode="验证码必须是6位数字":/^\d{6}$/.test(u.verificationCode)?d.verificationCode=void 0:d.verificationCode="验证码只能包含数字":d.verificationCode="请输入验证码"},Z=()=>{u.verificationCode=u.verificationCode.replace(/\D/g,""),u.verificationCode.length===6&&B()},N=async()=>{if(!(!g.value||f.value)){f.value=!0;try{const I=await $n(u.email,"REGISTER");if(I.success){v.value=60;const k=setInterval(()=>{v.value--,v.value<=0&&clearInterval(k)},1e3);a.value=""}else a.value=I.message||"发送验证码失败"}catch{a.value="发送验证码失败，请检查网络连接"}finally{f.value=!1}}},oe=async()=>{if(a.value="",y(),w(),T(),A(),B(),!(d.username||d.email||d.password||d.confirmPassword||d.verificationCode)){if(!r.value){a.value="请同意用户协议和隐私政策";return}i.value=!0;try{await new Promise(k=>setTimeout(k,1500));const I=await Od({username:u.username,password:u.password,email:u.email,verificationCode:u.verificationCode});I.success?(l.value="注册成功！即将跳转到登录页面...",c.value=!0,setTimeout(()=>{t.push({path:"/login",query:{message:"注册成功！请使用您的邮箱或手机号登录",type:"success"}})},2e3)):I.message.includes("用户名")?d.username=I.message:I.message.includes("邮箱")?d.email=I.message:I.message.includes("验证码")?d.verificationCode=I.message:a.value=I.message}catch{a.value="注册失败，请检查网络连接后重试"}finally{i.value=!1}}};return(I,k)=>{const z=ft("router-link");return b(),C("div",Tf,[k[27]||(k[27]=Le('<div class="background-decoration" data-v-4b8c8847><div class="circle circle-1" data-v-4b8c8847></div><div class="circle circle-2" data-v-4b8c8847></div><div class="circle circle-3" data-v-4b8c8847></div><div class="wave-pattern" data-v-4b8c8847></div></div>',1)),n("div",Ef,[n("div",Pf,[n("div",Af,[k[25]||(k[25]=n("div",{class:"form-header"},[n("h2",null,"开启健康之旅"),n("p",null,"创建您的账户，开始科学的营养管理")],-1)),c.value?(b(),C("div",Mf,[n("div",Lf,[k[8]||(k[8]=n("svg",{class:"success-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M9 12L11 14L15 10M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),n("span",null,S(l.value),1)])])):J("",!0),a.value?(b(),C("div",Of,[n("div",Rf,[k[9]||(k[9]=n("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),n("span",null,S(a.value),1)])])):J("",!0),n("form",{onSubmit:kt(oe,["prevent"]),class:"register-form"},[n("div",If,[k[11]||(k[11]=n("label",{for:"username",class:"form-label"},"用户名",-1)),n("div",Uf,[te(n("input",{id:"username","onUpdate:modelValue":k[0]||(k[0]=se=>u.username=se),type:"text",class:pe(["form-input",{error:d.username}]),placeholder:"请输入用户名",onBlur:y},null,34),[[de,u.username]]),k[10]||(k[10]=n("span",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M20 21V19C20 17.9391 19.5786 16.9217 18.8284 16.1716C18.0783 15.4214 17.0609 15 16 15H8C6.93913 15 5.92172 15.4214 5.17157 16.1716C4.42143 16.9217 4 17.9391 4 19V21",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("circle",{cx:"12",cy:"7",r:"4",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),d.username?(b(),C("span",Vf,S(d.username),1)):J("",!0)]),n("div",jf,[k[13]||(k[13]=n("label",{for:"email",class:"form-label"},"邮箱地址",-1)),n("div",Nf,[te(n("input",{id:"email","onUpdate:modelValue":k[1]||(k[1]=se=>u.email=se),type:"email",class:pe(["form-input",{error:d.email}]),placeholder:"请输入您的邮箱",onBlur:w},null,34),[[de,u.email]]),k[12]||(k[12]=n("span",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),d.email?(b(),C("span",Df,S(d.email),1)):J("",!0)]),n("div",Bf,[k[14]||(k[14]=n("label",{for:"verificationCode",class:"form-label"},"邮箱验证码",-1)),n("div",Ff,[te(n("input",{id:"verificationCode","onUpdate:modelValue":k[2]||(k[2]=se=>u.verificationCode=se),type:"text",class:pe(["form-input verification-input",{error:d.verificationCode}]),placeholder:"请输入6位验证码",maxlength:"6",onBlur:B,onInput:Z},null,34),[[de,u.verificationCode]]),n("button",{type:"button",class:pe(["send-code-btn",{disabled:!g.value||f.value}]),disabled:!g.value||f.value,onClick:N},S(f.value?"发送中...":v.value>0?`${v.value}s后重发`:"发送验证码"),11,Hf)]),d.verificationCode?(b(),C("span",zf,S(d.verificationCode),1)):J("",!0)]),n("div",Kf,[k[17]||(k[17]=n("label",{for:"password",class:"form-label"},"密码",-1)),n("div",Gf,[te(n("input",{id:"password","onUpdate:modelValue":k[3]||(k[3]=se=>u.password=se),type:s.value?"text":"password",class:pe(["form-input",{error:d.password}]),placeholder:"请设置密码（至少8位）",onBlur:T,onInput:U},null,42,Wf),[[ks,u.password]]),n("button",{type:"button",class:"password-toggle",onClick:k[4]||(k[4]=se=>s.value=!s.value)},[s.value?(b(),C("svg",qf,k[15]||(k[15]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",Zf,k[16]||(k[16]=[n("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),u.password?(b(),Xe(Sf,{key:0,password:u.password},null,8,["password"])):J("",!0),d.password?(b(),C("span",Jf,S(d.password),1)):J("",!0)]),n("div",Yf,[k[20]||(k[20]=n("label",{for:"confirmPassword",class:"form-label"},"确认密码",-1)),n("div",Qf,[te(n("input",{id:"confirmPassword","onUpdate:modelValue":k[5]||(k[5]=se=>u.confirmPassword=se),type:o.value?"text":"password",class:pe(["form-input",{error:d.confirmPassword}]),placeholder:"请再次输入密码",onBlur:A},null,42,Xf),[[ks,u.confirmPassword]]),n("button",{type:"button",class:"password-toggle",onClick:k[6]||(k[6]=se=>o.value=!o.value)},[o.value?(b(),C("svg",e1,k[18]||(k[18]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",t1,k[19]||(k[19]=[n("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),d.confirmPassword?(b(),C("span",s1,S(d.confirmPassword),1)):J("",!0)]),n("div",n1,[n("label",o1,[te(n("input",{type:"checkbox","onUpdate:modelValue":k[7]||(k[7]=se=>r.value=se)},null,512),[[Zs,r.value]]),k[21]||(k[21]=n("span",{class:"checkmark"},null,-1)),k[22]||(k[22]=n("span",{class:"checkbox-label"},[ee(" 我已阅读并同意 "),n("a",{href:"#",class:"terms-link"},"用户协议"),ee(" 和 "),n("a",{href:"#",class:"terms-link"},"隐私政策")],-1))])]),n("button",{type:"submit",class:"register-button",disabled:i.value||!r.value},[i.value?(b(),C("span",i1)):J("",!0),n("span",null,S(i.value?"注册中...":"创建账户"),1)],8,r1)],32),n("div",l1,[n("p",null,[k[24]||(k[24]=ee("已有账户？ ",-1)),ue(z,{to:"/login",class:"login-link"},{default:me(()=>k[23]||(k[23]=[ee("立即登录",-1)])),_:1,__:[23]})])])])]),k[26]||(k[26]=Le('<div class="brand-section" data-v-4b8c8847><div class="brand-content" data-v-4b8c8847><div class="brand-logo" data-v-4b8c8847><div class="logo-icon" data-v-4b8c8847><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-4b8c8847><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-4b8c8847></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-4b8c8847></circle></svg></div><h1 class="brand-title" data-v-4b8c8847>膳食营养分析平台</h1></div><div class="benefits-list" data-v-4b8c8847><div class="benefit-item" data-v-4b8c8847><div class="benefit-icon" data-v-4b8c8847>🎯</div><div class="benefit-content" data-v-4b8c8847><h3 data-v-4b8c8847>个性化目标</h3><p data-v-4b8c8847>根据您的身体状况和健康目标，制定专属营养计划</p></div></div><div class="benefit-item" data-v-4b8c8847><div class="benefit-icon" data-v-4b8c8847>📊</div><div class="benefit-content" data-v-4b8c8847><h3 data-v-4b8c8847>智能分析</h3><p data-v-4b8c8847>AI驱动的营养成分分析，让每一餐都更科学</p></div></div><div class="benefit-item" data-v-4b8c8847><div class="benefit-icon" data-v-4b8c8847>🏆</div><div class="benefit-content" data-v-4b8c8847><h3 data-v-4b8c8847>持续改进</h3><p data-v-4b8c8847>跟踪您的进步，不断优化饮食建议</p></div></div></div></div></div>',1))])])}}}),c1=Ue(a1,[["__scopeId","data-v-4b8c8847"]]);class u1{container=null;createContainer(){return this.container?this.container:(this.container=document.createElement("div"),this.container.className="message-container",this.container.style.cssText=`
      position: fixed;
      top: 20px;
      left: 50%;
      transform: translateX(-50%);
      z-index: 9999;
      pointer-events: none;
    `,document.body.appendChild(this.container),this.container)}createMessage(t,s={}){const{type:o="info",duration:r=3e3,position:i="top"}=s,l=document.createElement("div");l.className=`message message-${o}`,l.textContent=t;const c=`
      padding: 12px 20px;
      margin-bottom: 10px;
      border-radius: 6px;
      color: white;
      font-size: 14px;
      font-weight: 500;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
      opacity: 0;
      transform: translateY(-20px);
      transition: all 0.3s ease;
      pointer-events: auto;
      max-width: 400px;
      word-wrap: break-word;
    `,a={success:"background-color: #10b981;",error:"background-color: #ef4444;",warning:"background-color: #f59e0b;",info:"background-color: #3b82f6;"};l.style.cssText=c+a[o];const u=this.createContainer();return u.appendChild(l),requestAnimationFrame(()=>{l.style.opacity="1",l.style.transform="translateY(0)"}),setTimeout(()=>{l.style.opacity="0",l.style.transform="translateY(-20px)",setTimeout(()=>{l.parentNode&&l.parentNode.removeChild(l),u.children.length===0&&(document.body.removeChild(u),this.container=null)},300)},r),l}success(t,s){return this.createMessage(t,{...s,type:"success"})}error(t,s){return this.createMessage(t,{...s,type:"error"})}warning(t,s){return this.createMessage(t,{...s,type:"warning"})}info(t,s){return this.createMessage(t,{...s,type:"info"})}}const $e=new u1,d1={class:"forgot-password-container"},f1={class:"forgot-password-content"},p1={class:"back-to-login"},h1={class:"forgot-password-card"},v1={key:0,class:"error-alert"},g1={key:1,class:"success-alert"},m1={class:"form-group"},w1={class:"input-wrapper"},y1={key:0,class:"error-message"},b1=["disabled"],_1={key:0,class:"loading-spinner"},k1={class:"step-info"},C1={class:"form-group"},x1={class:"input-wrapper"},$1={key:0,class:"error-message"},S1={class:"form-group"},T1={class:"input-wrapper"},E1=["type"],P1={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},A1={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},M1={key:0,class:"error-message"},L1={class:"form-group"},O1={class:"input-wrapper"},R1=["type"],I1={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},U1={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},V1={key:0,class:"error-message"},j1={class:"resend-section"},N1=["disabled"],D1=["disabled"],B1={key:0,class:"loading-spinner"},F1=Ie({__name:"ForgotPasswordView",setup(e){const t=lt(),s=j(1),o=j(!1),r=j(!1),i=j(!1),l=j(""),c=j(""),a=j(!1),u=j(0),d=j(null),f=j(""),v=Me({email:"",verificationCode:"",newPassword:""}),g=Me({email:"",verificationCode:"",newPassword:"",confirmPassword:""}),_=()=>{const k=/^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\.[A-Za-z]{2,}$/;v.email?k.test(v.email)?g.email="":g.email="邮箱格式不正确":g.email="请输入邮箱地址"},y=()=>{v.verificationCode?v.verificationCode.length!==6?g.verificationCode="验证码应为6位数字":g.verificationCode="":g.verificationCode="请输入验证码"},w=()=>{v.newPassword?v.newPassword.length<6?g.newPassword="密码长度至少6位":g.newPassword="":g.newPassword="请输入新密码"},T=()=>{f.value?f.value!==v.newPassword?g.confirmPassword="两次输入的密码不一致":g.confirmPassword="":g.confirmPassword="请确认新密码"},A=async()=>{if(_(),!g.email){r.value=!0,l.value="";try{const k=await $n(v.email,"forgot_password");k.success?(s.value=2,I(),$e.success("验证码已发送，请查收邮件")):k.message==="邮箱未注册"?(g.email="该邮箱尚未注册，请先注册账号",l.value=""):l.value=k.message||"发送验证码失败"}catch{l.value="发送验证码失败，请重试"}finally{r.value=!1}}},U=async()=>{i.value=!0;try{const k=await $n(v.email,"forgot_password");k.success?(I(),$e.success("验证码已重新发送")):$e.error(k.message||"发送验证码失败")}catch{$e.error("发送验证码失败，请重试")}finally{i.value=!1}},B=async()=>{if(y(),w(),T(),!(g.verificationCode||g.newPassword||g.confirmPassword)){r.value=!0,l.value="";try{const k=await Ud(v);k.success?(a.value=!0,c.value="密码重置成功！即将跳转到登录页面...",setTimeout(()=>{t.push("/login")},3e3)):l.value=k.message||"密码重置失败"}catch{l.value="密码重置失败，请重试"}finally{r.value=!1}}},Z=()=>{s.value===1?A():B()},N=()=>{s.value=1,v.verificationCode="",v.newPassword="",f.value="",oe()},oe=()=>{Object.keys(g).forEach(k=>{g[k]=""}),l.value=""},I=()=>{u.value=60,d.value=setInterval(()=>{u.value--,u.value<=0&&(clearInterval(d.value),d.value=null)},1e3)};return Ss(()=>{d.value&&clearInterval(d.value)}),(k,z)=>{const se=ft("router-link");return b(),C("div",d1,[z[21]||(z[21]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),n("div",f1,[n("div",p1,[ue(se,{to:"/login",class:"back-link"},{default:me(()=>z[6]||(z[6]=[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 返回登录 ",-1)])),_:1,__:[6]})]),n("div",h1,[z[20]||(z[20]=Le('<div class="card-header" data-v-65da3c08><div class="logo" data-v-65da3c08><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-65da3c08><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-65da3c08></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-65da3c08></circle></svg></div><h1 class="card-title" data-v-65da3c08>重置密码</h1><p class="card-subtitle" data-v-65da3c08>请输入您的邮箱地址，我们将发送验证码帮助您重置密码</p></div>',1)),l.value?(b(),C("div",v1,[z[7]||(z[7]=n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),n("span",null,S(l.value),1)])):J("",!0),a.value?(b(),C("div",g1,[z[8]||(z[8]=n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M22 11.08V12A10 10 0 1 1 5.93 7.25",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,4 12,14.01 9,11.01",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),n("span",null,S(c.value),1)])):J("",!0),n("form",{onSubmit:kt(Z,["prevent"]),class:"forgot-password-form"},[s.value===1?(b(),C(ae,{key:0},[n("div",m1,[z[10]||(z[10]=n("label",{for:"email",class:"form-label"},"邮箱地址",-1)),n("div",w1,[te(n("input",{id:"email","onUpdate:modelValue":z[0]||(z[0]=H=>v.email=H),type:"email",class:pe(["form-input",{error:g.email}]),placeholder:"请输入您的邮箱地址",onBlur:_},null,34),[[de,v.email]]),z[9]||(z[9]=n("svg",{class:"input-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1))]),g.email?(b(),C("span",y1,S(g.email),1)):J("",!0)]),n("button",{type:"submit",class:"submit-button",disabled:r.value},[r.value?(b(),C("span",_1)):J("",!0),n("span",null,S(r.value?"发送中...":"发送验证码"),1)],8,b1)],64)):s.value===2?(b(),C(ae,{key:1},[n("div",k1,[n("p",null,[z[11]||(z[11]=ee("验证码已发送至 ",-1)),n("strong",null,S(v.email),1)]),n("button",{type:"button",onClick:N,class:"change-email-btn"},"更换邮箱")]),n("div",C1,[z[13]||(z[13]=n("label",{for:"verificationCode",class:"form-label"},"验证码",-1)),n("div",x1,[te(n("input",{id:"verificationCode","onUpdate:modelValue":z[1]||(z[1]=H=>v.verificationCode=H),type:"text",class:pe(["form-input",{error:g.verificationCode}]),placeholder:"请输入6位验证码",maxlength:"6",onBlur:y},null,34),[[de,v.verificationCode]]),z[12]||(z[12]=n("svg",{class:"input-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("rect",{x:"3",y:"11",width:"18",height:"11",rx:"2",ry:"2",stroke:"currentColor","stroke-width":"2"}),n("circle",{cx:"12",cy:"16",r:"1",fill:"currentColor"}),n("path",{d:"M7 11V7A5 5 0 0 1 17 7V11",stroke:"currentColor","stroke-width":"2"})],-1))]),g.verificationCode?(b(),C("span",$1,S(g.verificationCode),1)):J("",!0)]),n("div",S1,[z[16]||(z[16]=n("label",{for:"newPassword",class:"form-label"},"新密码",-1)),n("div",T1,[te(n("input",{id:"newPassword","onUpdate:modelValue":z[2]||(z[2]=H=>v.newPassword=H),type:o.value?"text":"password",class:pe(["form-input",{error:g.newPassword}]),placeholder:"请输入新密码（至少6位）",onBlur:w},null,42,E1),[[ks,v.newPassword]]),n("button",{type:"button",class:"password-toggle",onClick:z[3]||(z[3]=H=>o.value=!o.value)},[o.value?(b(),C("svg",P1,z[14]||(z[14]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",A1,z[15]||(z[15]=[n("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),g.newPassword?(b(),C("span",M1,S(g.newPassword),1)):J("",!0)]),n("div",L1,[z[19]||(z[19]=n("label",{for:"confirmPassword",class:"form-label"},"确认新密码",-1)),n("div",O1,[te(n("input",{id:"confirmPassword","onUpdate:modelValue":z[4]||(z[4]=H=>f.value=H),type:o.value?"text":"password",class:pe(["form-input",{error:g.confirmPassword}]),placeholder:"请再次输入新密码",onBlur:T},null,42,R1),[[ks,f.value]]),n("button",{type:"button",class:"password-toggle",onClick:z[5]||(z[5]=H=>o.value=!o.value)},[o.value?(b(),C("svg",I1,z[17]||(z[17]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M1 1L23 23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M10.584 10.587A2 2 0 0 0 13.415 13.414",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",U1,z[18]||(z[18]=[n("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)])))])]),g.confirmPassword?(b(),C("span",V1,S(g.confirmPassword),1)):J("",!0)]),n("div",j1,[n("button",{type:"button",onClick:U,disabled:u.value>0||i.value,class:"resend-btn"},S(u.value>0?`${u.value}秒后重发`:"重新发送验证码"),9,N1)]),n("button",{type:"submit",class:"submit-button",disabled:r.value},[r.value?(b(),C("span",B1)):J("",!0),n("span",null,S(r.value?"重置中...":"重置密码"),1)],8,D1)],64)):J("",!0)],32)])])])}}}),H1=Ue(F1,[["__scopeId","data-v-65da3c08"]]),z1={class:"dashboard-container"},K1={class:"main-content"},G1={class:"welcome-section"},W1={class:"welcome-content"},q1={class:"welcome-title"},Z1={class:"quick-actions"},J1={class:"stats-section"},Y1={class:"stats-grid"},Q1={class:"stat-card"},X1={class:"stat-info"},ep={class:"stat-number"},tp={class:"stat-progress"},sp={class:"stat-card"},np={class:"stat-info"},op={class:"stat-number"},rp={class:"stat-card"},ip={class:"stat-info"},lp={class:"stat-number"},ap={class:"stat-progress"},cp={class:"stat-card"},up={class:"stat-info"},dp={class:"stat-number"},fp={class:"features-section"},pp={class:"features-grid"},hp=["innerHTML"],vp={class:"feature-title"},gp={class:"feature-description"},mp={class:"activity-section"},wp={class:"activity-list"},yp={key:0,class:"no-activity"},bp={class:"activity-icon"},_p={class:"activity-content"},kp={class:"activity-title"},Cp={class:"activity-description"},xp={class:"activity-time"},$p=Ie({__name:"DashboardView",setup(e){const t=lt(),{user:s,isLoggedIn:o,logout:r,updateUserState:i}=Nt(),l=j(),c=j(!1),a=Me({calories:0,meals:0,goalProgress:0,streak:0}),u=j([{id:1,title:"营养分析",description:"分析食物营养成分，制定健康饮食计划",icon:"🥗",path:"/nutrition",status:"developing",statusText:"开发中"},{id:2,title:"膳食记录",description:"记录每日饮食，追踪营养摄入情况",icon:"🍽️",path:"/meals",status:"developing",statusText:"开发中"},{id:3,title:"健康目标",description:"设定和管理个人健康目标",icon:"🎯",path:"/goals",status:"developing",statusText:"开发中"},{id:4,title:"健康报告",description:"查看详细的健康分析报告",icon:"📊",path:"/reports",status:"developing",statusText:"开发中"},{id:5,title:"个人资料",description:"管理个人信息和偏好设置",icon:"👤",path:"/profile",status:"developing",statusText:"开发中"},{id:6,title:"系统设置",description:"调整应用设置和通知偏好",icon:"⚙️",path:"/settings",status:"developing",statusText:"开发中"}]),d=j([]),f=()=>{c.value||l.value?.click()},v=async y=>{const w=y.target,T=w.files?.[0];if(!T){c.value=!1;return}if(!Ju(T)){$e.error(`只支持 ${ed()} 格式的图片`);return}if(!Yu(T)){$e.error(`文件大小不能超过 ${Xu()}`);return}c.value=!0;try{const A=await ol(T);A.success?(s.value&&(s.value.avatar=A.data,je.setUser(s.value)),$e.success("头像上传成功"),i()):$e.error(A.message||"头像上传失败")}catch{$e.error("头像上传失败，请稍后重试")}finally{w.value="",c.value=!1}},g=async()=>{},_=()=>{r(),i(),t.push("/")};return jt(()=>{if(!o.value){t.push("/login");return}g()}),(y,w)=>{const T=ft("router-link");return b(),C("div",z1,[ue(tn,{"show-avatar":!0,"show-logout-button":!0,onAvatarClick:f,onLogout:_}),w[18]||(w[18]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),n("main",K1,[n("section",G1,[n("div",W1,[n("h1",q1,"欢迎回来，"+S(fe(s)?.username)+"！",1),w[2]||(w[2]=n("p",{class:"welcome-subtitle"},"开始您的健康饮食之旅，让我们一起追踪您的营养目标",-1)),n("div",Z1,[ue(T,{to:"/nutrition",class:"quick-action-btn primary"},{default:me(()=>w[0]||(w[0]=[n("span",{class:"action-icon"},"🥗",-1),n("span",null,"营养分析",-1)])),_:1,__:[0]}),ue(T,{to:"/meals",class:"quick-action-btn secondary"},{default:me(()=>w[1]||(w[1]=[n("span",{class:"action-icon"},"🍽️",-1),n("span",null,"记录膳食",-1)])),_:1,__:[1]})])])]),n("section",J1,[n("div",Y1,[n("div",Q1,[w[5]||(w[5]=n("div",{class:"stat-icon"},"📊",-1)),n("div",X1,[w[3]||(w[3]=n("h3",null,"今日营养摄入",-1)),n("p",ep,S(a.calories||0),1),w[4]||(w[4]=n("p",{class:"stat-unit"},"千卡",-1)),n("div",tp,[n("div",{class:"progress-bar",style:Ot({width:`${Math.min(100,(a.calories||0)/2e3*100)}%`})},null,4)])])]),n("div",sp,[w[8]||(w[8]=n("div",{class:"stat-icon"},"🍽️",-1)),n("div",np,[w[6]||(w[6]=n("h3",null,"今日膳食记录",-1)),n("p",op,S(a.meals||0),1),w[7]||(w[7]=n("p",{class:"stat-unit"},"餐",-1))])]),n("div",rp,[w[11]||(w[11]=n("div",{class:"stat-icon"},"🎯",-1)),n("div",ip,[w[9]||(w[9]=n("h3",null,"健康目标",-1)),n("p",lp,S(a.goalProgress||0)+"%",1),w[10]||(w[10]=n("p",{class:"stat-unit"},"完成度",-1)),n("div",ap,[n("div",{class:"progress-bar",style:Ot({width:`${a.goalProgress||0}%`})},null,4)])])]),n("div",cp,[w[14]||(w[14]=n("div",{class:"stat-icon"},"📈",-1)),n("div",up,[w[12]||(w[12]=n("h3",null,"连续记录",-1)),n("p",dp,S(a.streak||0),1),w[13]||(w[13]=n("p",{class:"stat-unit"},"天",-1))])])])]),n("section",fp,[w[15]||(w[15]=n("h2",{class:"section-title"},"功能中心",-1)),n("div",pp,[(b(!0),C(ae,null,Ve(u.value,A=>(b(),Xe(T,{key:A.id,to:A.path,class:"feature-card"},{default:me(()=>[n("div",{class:"feature-icon",innerHTML:A.icon},null,8,hp),n("h3",vp,S(A.title),1),n("p",gp,S(A.description),1),n("div",{class:pe(["feature-status",A.status])},S(A.statusText),3)]),_:2},1032,["to"]))),128))])]),n("section",mp,[w[17]||(w[17]=n("h2",{class:"section-title"},"最近活动",-1)),n("div",wp,[d.value.length===0?(b(),C("div",yp,w[16]||(w[16]=[n("div",{class:"no-activity-icon"},"📝",-1),n("p",null,"暂无活动记录",-1),n("p",{class:"no-activity-hint"},"开始记录您的膳食和营养分析吧！",-1)]))):J("",!0),(b(!0),C(ae,null,Ve(d.value,A=>(b(),C("div",{key:A.id,class:"activity-item"},[n("div",bp,S(A.icon),1),n("div",_p,[n("h4",kp,S(A.title),1),n("p",Cp,S(A.description),1),n("span",xp,S(A.time),1)])]))),128))])])]),n("input",{ref_key:"avatarFileInput",ref:l,type:"file",accept:"image/jpeg,image/jpg,image/png,image/gif",style:{display:"none"},onChange:v},null,544)])}}}),Sp=Ue($p,[["__scopeId","data-v-eac7654d"]]),$t="http://localhost:8080/api/admin";async function as(e,t={}){return(await Ts(e,t)).json()}async function Tp(e){const s=await(await fetch(`${$t}/login`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:o,...r}=s.data;o&&Ce.login(o,{...r,userType:"ADMIN"})}return s}async function Ep(e){const s=await(await fetch(`${$t}/login-with-code`,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(e)})).json();if(s.success&&s.data){const{token:o,...r}=s.data;o&&Ce.login(o,{...r,userType:"ADMIN"})}return s}async function Pp(e){return(await fetch(`${$t}/send-login-code?email=${encodeURIComponent(e)}`,{method:"POST"})).json()}async function Ap(e){return as(`${$t}/${e}`,{method:"DELETE"})}async function Mp(e){const t=new URLSearchParams;return e.page&&t.append("page",e.page.toString()),e.size&&t.append("size",e.size.toString()),e.username&&t.append("username",e.username),e.realName&&t.append("realName",e.realName),e.status!==void 0&&t.append("status",e.status.toString()),as(`${$t}/list?${t.toString()}`)}async function Lp(e,t){return as(`${$t}/${e}/status?status=${t}`,{method:"PUT"})}async function Op(e){const t=new URLSearchParams;return e.page&&t.append("page",e.page.toString()),e.size&&t.append("size",e.size.toString()),e.username&&t.append("username",e.username),e.email&&t.append("email",e.email),e.phone&&t.append("phone",e.phone),e.status!==void 0&&t.append("status",e.status.toString()),as(`${$t}/users?${t.toString()}`)}async function Rp(e,t){return as(`${$t}/users/${e}/status?status=${t}`,{method:"PUT"})}async function Ip(e){return as(`${$t}/users/${e}`,{method:"DELETE"})}async function Up(){return as(`${$t}/users/stats`)}const Vp={class:"login-container"},jp={class:"login-content"},Np={class:"form-section"},Dp={class:"form-container"},Bp={class:"login-mode-switch"},Fp={key:0,class:"global-error-message"},Hp={class:"error-content"},zp={class:"form-group"},Kp={class:"input-wrapper"},Gp={key:0,class:"error-message"},Wp={class:"form-group"},qp={class:"input-wrapper"},Zp=["type"],Jp={key:0,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Yp={key:1,viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Qp={key:0,class:"error-message"},Xp={class:"form-group"},eh={class:"input-wrapper"},th={key:0,class:"error-message"},sh={class:"form-group"},nh={class:"input-wrapper verification-wrapper"},oh=["disabled"],rh={key:0,class:"loading-spinner"},ih={key:1},lh={key:2},ah={key:0,class:"error-message"},ch=["disabled"],uh={key:0,class:"loading-spinner"},dh={class:"form-footer"},fh={class:"footer-links"},ph=Ie({__name:"AdminLoginView",setup(e){const t=lt(),s=j("password"),o=j(!1),r=j(0),i=j(null),l=Me({username:"",password:""}),c=Me({email:"",verificationCode:""}),a=Me({username:"",password:"",email:"",verificationCode:""}),u=j(!1),d=j(!1),f=j(""),v=()=>{d.value=!d.value},g=()=>{l.username.trim()?a.username="":a.username="请输入管理员用户名"},_=()=>{l.password.trim()?a.password="":a.password="请输入密码"},y=()=>{const Z=/^[^\s@]+@[^\s@]+\.[^\s@]+$/;c.email.trim()?Z.test(c.email)?a.email="":a.email="请输入有效的邮箱地址":a.email="请输入邮箱地址"},w=()=>{c.verificationCode.trim()?c.verificationCode.length!==6?a.verificationCode="验证码必须是6位数字":a.verificationCode="":a.verificationCode="请输入验证码"},T=()=>{s.value=s.value==="password"?"code":"password",f.value="",Object.keys(a).forEach(Z=>{a[Z]=""})},A=async()=>{if(y(),!a.email){o.value=!0;try{const Z=await Pp(c.email);Z.success?(r.value=60,i.value=window.setInterval(()=>{r.value--,r.value<=0&&i.value&&(clearInterval(i.value),i.value=null)},1e3)):f.value=Z.message}catch(Z){f.value=Z.message||"发送验证码失败，请重试"}finally{o.value=!1}}},U=()=>(f.value="",s.value==="password"?(g(),_(),!a.username&&!a.password):(y(),w(),!a.email&&!a.verificationCode)),B=async()=>{if(U()){u.value=!0;try{let Z;if(s.value==="password"?Z=await Tp({username:l.username,password:l.password}):Z=await Ep({email:c.email,verificationCode:c.verificationCode}),Z.success&&Z.data){const{token:N,...oe}=Z.data;N&&Ce.login(N,{...oe,userType:"ADMIN"}),t.push("/admin/dashboard")}else f.value=Z.message}catch(Z){f.value=Z.message||"登录失败，请检查网络连接后重试"}finally{u.value=!1}}};return(Z,N)=>{const oe=ft("router-link");return b(),C("div",Vp,[N[19]||(N[19]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),n("div",jp,[N[18]||(N[18]=Le('<div class="brand-section" data-v-f13a410e><div class="brand-logo" data-v-f13a410e><div class="logo-container" data-v-f13a410e><div class="logo-circle" data-v-f13a410e><span class="logo-text" data-v-f13a410e>🛡️</span></div><div class="logo-ripple" data-v-f13a410e></div></div><h1 class="brand-title" data-v-f13a410e><span class="title-char" style="animation-delay:0.1s;" data-v-f13a410e>膳</span><span class="title-char" style="animation-delay:0.2s;" data-v-f13a410e>食</span><span class="title-char" style="animation-delay:0.3s;" data-v-f13a410e>营</span><span class="title-char" style="animation-delay:0.4s;" data-v-f13a410e>养</span><span class="title-char" style="animation-delay:0.5s;" data-v-f13a410e>分</span><span class="title-char" style="animation-delay:0.6s;" data-v-f13a410e>析</span><span class="title-char" style="animation-delay:0.7s;" data-v-f13a410e>平</span><span class="title-char" style="animation-delay:0.8s;" data-v-f13a410e>台</span></h1><p class="brand-subtitle" data-v-f13a410e>管理后台 - 系统管理中心</p></div><div class="feature-highlights" data-v-f13a410e><div class="feature-item" data-v-f13a410e><div class="feature-icon" data-v-f13a410e>👥</div><div class="feature-content" data-v-f13a410e><h3 data-v-f13a410e>用户管理</h3><p data-v-f13a410e>全面的用户账户管理</p></div></div><div class="feature-item" data-v-f13a410e><div class="feature-icon" data-v-f13a410e>📊</div><div class="feature-content" data-v-f13a410e><h3 data-v-f13a410e>数据统计</h3><p data-v-f13a410e>详细的数据分析报告</p></div></div><div class="feature-item" data-v-f13a410e><div class="feature-icon" data-v-f13a410e>⚙️</div><div class="feature-content" data-v-f13a410e><h3 data-v-f13a410e>系统配置</h3><p data-v-f13a410e>灵活的系统参数设置</p></div></div></div></div>',1)),n("div",Np,[n("div",Dp,[N[17]||(N[17]=n("div",{class:"form-header"},[n("h2",{class:"form-title"},"管理员登录"),n("div",{class:"title-underline"}),n("p",{class:"form-subtitle"},"请使用管理员账户登录系统")],-1)),n("div",Bp,[s.value==="code"?(b(),C("button",{key:0,type:"button",class:"mode-button",onClick:T},N[4]||(N[4]=[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M12 1L15.09 8.26L22 9L15.09 9.74L12 17L8.91 9.74L2 9L8.91 8.26L12 1Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 使用密码登录 ",-1)]))):J("",!0),s.value==="password"?(b(),C("button",{key:1,type:"button",class:"mode-button",onClick:T},N[5]||(N[5]=[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 使用验证码登录 ",-1)]))):J("",!0)]),f.value?(b(),C("div",Fp,[n("div",Hp,[N[6]||(N[6]=n("svg",{class:"error-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"15",y1:"9",x2:"9",y2:"15",stroke:"currentColor","stroke-width":"2"}),n("line",{x1:"9",y1:"9",x2:"15",y2:"15",stroke:"currentColor","stroke-width":"2"})],-1)),n("span",null,S(f.value),1)])])):J("",!0),n("form",{onSubmit:kt(B,["prevent"]),class:"login-form"},[s.value==="password"?(b(),C(ae,{key:0},[n("div",zp,[N[8]||(N[8]=n("label",{for:"username",class:"form-label"},"管理员用户名",-1)),n("div",Kp,[te(n("input",{id:"username","onUpdate:modelValue":N[0]||(N[0]=I=>l.username=I),type:"text",class:pe(["form-input",{error:a.username}]),placeholder:"请输入管理员用户名",onBlur:g},null,34),[[de,l.username]]),N[7]||(N[7]=n("span",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M12,4A4,4 0 0,1 16,8A4,4 0 0,1 12,12A4,4 0 0,1 8,8A4,4 0 0,1 12,4M12,14C16.42,14 20,15.79 20,18V20H4V18C4,15.79 7.58,14 12,14Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),a.username?(b(),C("span",Gp,S(a.username),1)):J("",!0)]),n("div",Wp,[N[12]||(N[12]=n("label",{for:"password",class:"form-label"},"密码",-1)),n("div",qp,[te(n("input",{id:"password","onUpdate:modelValue":N[1]||(N[1]=I=>l.password=I),type:d.value?"text":"password",class:pe(["form-input",{error:a.password}]),placeholder:"请输入密码",onBlur:_},null,42,Zp),[[ks,l.password]]),N[11]||(N[11]=Le('<span class="input-icon" data-v-f13a410e><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-f13a410e><rect x="3" y="11" width="18" height="11" rx="2" ry="2" stroke="currentColor" stroke-width="2" data-v-f13a410e></rect><circle cx="12" cy="16" r="1" stroke="currentColor" stroke-width="2" data-v-f13a410e></circle><path d="M7 11V7a5 5 0 0 1 10 0v4" stroke="currentColor" stroke-width="2" data-v-f13a410e></path></svg></span>',1)),n("button",{type:"button",class:"password-toggle",onClick:v},[d.value?(b(),C("svg",Jp,N[9]||(N[9]=[n("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20C7 20 2.73 16.39 1 12A18.45 18.45 0 0 1 5.06 5.06M9.9 4.24A9.12 9.12 0 0 1 12 4C17 4 21.27 7.61 23 12A18.5 18.5 0 0 1 19.42 16.42",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("line",{x1:"1",y1:"1",x2:"23",y2:"23",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",Yp,N[10]||(N[10]=[n("path",{d:"M1 12S5 4 12 4s11 8 11 8-4 8-11 8S1 12 1 12z",stroke:"currentColor","stroke-width":"2"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2"},null,-1)])))])]),a.password?(b(),C("span",Qp,S(a.password),1)):J("",!0)])],64)):(b(),C(ae,{key:1},[n("div",Xp,[N[14]||(N[14]=n("label",{for:"email",class:"form-label"},"管理员邮箱",-1)),n("div",eh,[te(n("input",{id:"email","onUpdate:modelValue":N[2]||(N[2]=I=>c.email=I),type:"email",class:pe(["form-input",{error:a.email}]),placeholder:"请输入管理员邮箱",onBlur:y},null,34),[[de,c.email]]),N[13]||(N[13]=n("span",{class:"input-icon"},[n("svg",{viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M4 4H20C21.1 4 22 4.9 22 6V18C22 19.1 21.1 20 20 20H4C2.9 20 2 19.1 2 18V6C2 4.9 2.9 4 4 4Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("polyline",{points:"22,6 12,13 2,6",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})])],-1))]),a.email?(b(),C("span",th,S(a.email),1)):J("",!0)]),n("div",sh,[N[15]||(N[15]=n("label",{for:"verificationCode",class:"form-label"},"邮箱验证码",-1)),n("div",nh,[te(n("input",{id:"verificationCode","onUpdate:modelValue":N[3]||(N[3]=I=>c.verificationCode=I),type:"text",class:pe(["form-input verification-input",{error:a.verificationCode}]),placeholder:"请输入6位验证码",maxlength:"6",onBlur:w},null,34),[[de,c.verificationCode]]),n("button",{type:"button",class:pe(["send-code-button",{loading:o.value,disabled:r.value>0}]),disabled:o.value||r.value>0||!c.email,onClick:A},[o.value?(b(),C("span",rh)):r.value>0?(b(),C("span",ih,S(r.value)+"s",1)):(b(),C("span",lh,"发送验证码"))],10,oh)]),a.verificationCode?(b(),C("span",ah,S(a.verificationCode),1)):J("",!0)])],64)),n("button",{type:"submit",class:"login-button",disabled:u.value},[u.value?(b(),C("span",uh)):J("",!0),n("span",null,S(u.value?"登录中...":"登录管理后台"),1)],8,ch)],32),n("div",dh,[n("div",fh,[ue(oe,{to:"/",class:"back-link"},{default:me(()=>N[16]||(N[16]=[ee("← 返回首页",-1)])),_:1,__:[16]})])])])])])])}}}),hh=Ue(ph,[["__scopeId","data-v-f13a410e"]]),vh={class:"admin-dashboard"},gh={class:"sidebar"},mh={class:"sidebar-nav"},wh=["onClick"],yh={class:"nav-icon"},bh={class:"nav-text"},_h={class:"sidebar-footer"},kh={class:"admin-info"},Ch={class:"admin-details"},xh={class:"admin-name"},$h={class:"admin-role"},Sh={class:"main-content"},Th={class:"top-header"},Eh={class:"header-left"},Ph={class:"page-title"},Ah={class:"header-right"},Mh={class:"current-time"},Lh={class:"content-area"},Oh={key:0,class:"overview-content"},Rh={class:"stats-grid"},Ih={class:"stat-card"},Uh={class:"stat-info"},Vh={class:"stat-number"},jh={class:"stat-card"},Nh={class:"stat-info"},Dh={class:"stat-number"},Bh={class:"stat-detail"},Fh={class:"stat-card"},Hh={class:"stat-info"},zh={class:"stat-number"},Kh={class:"stat-card"},Gh={class:"stat-info"},Wh={class:"stat-status"},qh={class:"recent-activities"},Zh={class:"activity-list"},Jh={key:0,class:"no-activities"},Yh={class:"activity-time"},Qh={class:"activity-content"},Xh={key:1,class:"admins-content"},ev={class:"table-container"},tv={class:"data-table"},sv={class:"role-badge"},nv={class:"action-buttons"},ov=["onClick"],rv=["onClick"],iv={key:2,class:"features-content"},lv={class:"content-header"},av={class:"table-container"},cv={class:"data-table"},uv={class:"description-cell"},dv={class:"highlights-cell"},fv={class:"action-buttons"},pv=["onClick"],hv=["onClick"],vv=["onClick"],gv={key:3,class:"users-content"},mv={class:"content-header"},wv={class:"search-filters"},yv={class:"table-container"},bv={class:"data-table"},_v={class:"action-buttons"},kv=["onClick"],Cv=["onClick"],xv={class:"pagination-container"},$v={class:"pagination-info"},Sv={class:"pagination-controls"},Tv=["disabled"],Ev={class:"page-numbers"},Pv=["onClick"],Av=["disabled"],Mv={key:4,class:"dietary-preferences-content"},Lv={class:"content-header"},Ov={class:"table-container"},Rv={class:"data-table"},Iv={class:"action-buttons"},Uv=["onClick"],Vv=["onClick"],jv={key:5,class:"health-goals-content"},Nv={class:"content-header"},Dv={class:"table-container"},Bv={class:"data-table"},Fv={class:"action-buttons"},Hv=["onClick"],zv=["onClick"],Kv={key:6,class:"placeholder-content"},Gv={class:"modal-header"},Wv={class:"form-group"},qv={class:"form-group"},Zv={class:"form-group"},Jv={class:"form-group"},Yv={class:"form-row"},Qv={class:"form-group"},Xv={class:"form-group"},e0=["value"],t0={class:"modal-actions"},s0=["disabled"],n0={class:"modal-header"},o0={class:"form-group"},r0={class:"form-group"},i0={class:"form-group"},l0={class:"form-row"},a0={class:"form-group"},c0={class:"form-group"},u0={class:"modal-actions"},d0=["disabled"],f0={class:"modal-header"},p0={class:"form-group"},h0={class:"form-group"},v0={class:"form-group"},g0={class:"form-row"},m0={class:"form-group"},w0={class:"form-group"},y0={class:"modal-actions"},b0=["disabled"],_0=Ie({__name:"AdminDashboardView",setup(e){const t=lt(),s=j("overview"),o=j(""),r=j(null),i=j([]),l=j([]),c=j(!1),a=j(null),u=j(!1),d=j(""),f=j([]),v=j({totalUsers:0,activeUsers:0,inactiveUsers:0,todayRegistered:0}),g=Me({username:"",email:"",status:void 0}),_=Me({page:1,size:10,total:0}),y=j([]),w=[{value:1,label:"启用"},{value:0,label:"禁用"}],T=[{key:"overview",icon:"📊",label:"概览"},{key:"admins",icon:"👥",label:"管理员管理"},{key:"users",icon:"👤",label:"用户管理"},{key:"features",icon:"⭐",label:"核心功能管理"},{key:"dietary-preferences",icon:"🥗",label:"饮食偏好管理"},{key:"health-goals",icon:"🎯",label:"健康目标管理"}],A=Me({adminCount:0,userCount:0,activeToday:0,systemStatus:"正常运行"}),U=Me({title:"",description:"",icon:"",highlights:[],sortOrder:1,status:1}),B=j([]),Z=j(!1),N=j(null),oe=j(!1),I=Me({code:"",name:"",description:"",sortOrder:1,status:1}),k=j([]),z=j(!1),se=j(null),H=j(!1),P=Me({code:"",name:"",description:"",sortOrder:1,status:1}),le=()=>{const E=new Date;o.value=E.toLocaleString("zh-CN")},Oe=()=>({overview:"系统概览",admins:"管理员管理",users:"用户管理",features:"核心功能管理","dietary-preferences":"饮食偏好管理","health-goals":"健康目标管理"})[s.value]||"未知页面",Ae=E=>new Date(E).toLocaleString("zh-CN"),be=E=>new Date(E).toLocaleTimeString("zh-CN",{hour:"2-digit",minute:"2-digit"}),ge=()=>{const E=Ce.getCurrentUser();E&&E.userType==="ADMIN"?r.value=E:t.push("/admin/login")},nt=async()=>{try{const E=await Mp({page:1,size:100});E.success&&E.data&&(i.value=E.data.records,A.adminCount=E.data.total)}catch{}},ot=async()=>{try{const E=await fd({page:1,size:100});E.success&&E.data&&(l.value=E.data.records)}catch{}},et=async()=>{try{const E=await Up();E.success&&E.data&&(v.value=E.data,A.userCount=E.data.totalUsers,A.activeToday=E.data.todayRegistered)}catch{}},He=async()=>{try{y.value=[]}catch{}},Dt=async E=>{try{const m=E.status===1?0:1;await Lp(E.id,m),nt()}catch{}},Bt=async E=>{if(confirm(`确定要删除管理员 ${E.realName} 吗？`))try{await Ap(E.id),nt()}catch{}},cs=()=>{Ce.logout(),t.push("/admin/login")},tt=E=>{a.value=E,U.title=E.title,U.description=E.description,U.icon=E.icon,U.highlights=[...E.highlights],U.sortOrder=E.sortOrder,U.status=E.status,d.value=E.highlights.join(`
`),c.value=!0},M=()=>{c.value=!1,a.value=null,Object.assign(U,{title:"",description:"",icon:"",highlights:[],sortOrder:1,status:1}),d.value=""},q=async()=>{u.value=!0;try{U.highlights=d.value.split(`
`).filter(m=>m.trim());const E={title:U.title,description:U.description,icon:U.icon,highlights:U.highlights,sortOrder:U.sortOrder,status:U.status};a.value?await ud(a.value.id,E):await cd(E),M(),ot()}catch{}finally{u.value=!1}},G=async E=>{try{const m=E.status===1?0:1;await pd(E.id,m),ot()}catch{}},Y=async E=>{if(confirm(`确定要删除核心功能 ${E.title} 吗？`))try{await dd(E.id),ot()}catch{}},ve=async()=>{try{const E={page:_.page,size:_.size,username:g.username||void 0,email:g.email||void 0,status:g.status},m=await Op(E);m.success&&m.data&&(f.value=m.data.records,_.total=m.data.total)}catch{}},p=async E=>{try{const m=E.status===1?0:1;await Rp(E.id,m),ve(),et()}catch{}},h=async E=>{if(confirm(`确定要删除用户 ${E.username} 吗？`))try{await Ip(E.id),ve(),et()}catch{}},x=()=>{_.page=1,ve()},L=()=>{Object.assign(g,{username:"",email:"",status:void 0}),_.page=1,ve()},R=E=>{_.page=E,ve()},O=E=>{_.size=E,_.page=1,ve()},K=(E,m)=>{const $=[];let De=Math.max(1,E-Math.floor(2.5)),St=Math.min(m,De+5-1);St-De+1<5&&(De=Math.max(1,St-5+1));for(let Nn=De;Nn<=St;Nn++)$.push(Nn);return $},F=j(),D=j(!1),V=()=>{D.value||F.value?.click()},X=async E=>{const m=E.target,$=m.files?.[0];if(!(!$||D.value)&&W($)){D.value=!0;try{const ie=new FormData;ie.append("file",$);const St=await(await fetch("http://localhost:8080/api/files/avatar/admin",{method:"POST",headers:{Authorization:`Bearer ${rt.getToken()}`},body:ie})).json();St.success?(r.value&&(r.value.avatar=St.data,je.setUser(r.value)),$e.success("头像上传成功")):$e.error(St.message||"头像上传失败")}catch{$e.error("头像上传失败，请重试")}finally{D.value=!1,m.value=""}}},W=E=>{if(!["image/jpeg","image/jpg","image/png","image/gif"].includes(E.type))return $e.error("只支持 JPG、PNG、GIF 格式的图片"),!1;const $=10*1024*1024;return E.size>$?($e.error("文件大小不能超过 10MB"),!1):!0};jt(()=>{const E=rt.getToken(),m=Ce.getCurrentUser();if(!E||!m||m.userType!=="ADMIN"){console.error("未登录或非管理员用户，重定向到登录页面"),t.push("/admin/login");return}try{const ie=E.split(".");if(ie.length!==3){console.error("Token格式无效，重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}const De=JSON.parse(atob(ie[1])),St=Math.floor(Date.now()/1e3);if(De.exp&&De.exp<St){console.error("Token已过期，重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}}catch{console.error("Token解析失败，重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}ge(),nt(),ot(),ve(),et(),He(),le();const $=setInterval(le,1e3);Ss(()=>{clearInterval($)})});const Q=async()=>{try{const E=rt.getToken();if(console.log("Token:",E?"exists":"missing"),!E){console.error("Token缺失，重定向到管理员登录页面"),t.push("/admin/login");return}const m=await fetch("http://localhost:8080/api/health-goals/admin/all",{headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}});if(console.log("Response status:",m.status),console.log("Response headers:",m.headers.get("content-type")),!m.ok){if(m.status===401||m.status===403){console.error("认证失败，清除token并重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}const De=await m.text();console.error("API Error:",De);return}const $=m.headers.get("content-type");if(!$||!$.includes("application/json")){console.error("服务器返回非JSON响应，可能是认证问题"),Ce.logout(),t.push("/admin/login");return}const ie=await m.json();ie.success?B.value=ie.data:console.error("API返回错误:",ie.message)}catch(E){console.error("加载健康目标失败:",E),E instanceof TypeError&&E.message.includes("fetch")&&console.error("网络连接失败")}},re=E=>{N.value=E,I.code=E.code,I.name=E.name,I.description=E.description||"",I.sortOrder=E.sortOrder,I.status=E.status,Z.value=!0},he=async E=>{if(confirm(`确定要删除健康目标 "${E.name}" 吗？`))try{const m=rt.getToken();if(!m){console.error("Token缺失，重定向到管理员登录页面"),t.push("/admin/login");return}const $=await fetch(`http://localhost:8080/api/health-goals/admin/${E.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${m}`,"Content-Type":"application/json"}});if(!$.ok&&($.status===401||$.status===403)){console.error("认证失败，清除token并重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}(await $.json()).success&&Q()}catch(m){console.error("删除健康目标失败:",m)}},we=()=>{Z.value=!1,N.value=null,Object.assign(I,{code:"",name:"",description:"",sortOrder:1,status:1})},ye=async()=>{if(!oe.value){oe.value=!0;try{const E=rt.getToken();if(!E){console.error("Token缺失，重定向到管理员登录页面"),t.push("/admin/login");return}const m=N.value?`http://localhost:8080/api/health-goals/admin/${N.value.id}`:"http://localhost:8080/api/health-goals/admin/create",$=N.value?"PUT":"POST",ie=await fetch(m,{method:$,headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify(I)});if(!ie.ok&&(ie.status===401||ie.status===403)){console.error("认证失败，清除token并重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}(await ie.json()).success&&(we(),Q())}catch(E){console.error("保存健康目标失败:",E)}finally{oe.value=!1}}},Ne=async()=>{try{const E=rt.getToken();if(console.log("Token:",E?"exists":"missing"),!E){console.error("Token缺失，重定向到管理员登录页面"),t.push("/admin/login");return}const m=await fetch("http://localhost:8080/api/dietary-preferences/admin/all",{headers:{Authorization:`Bearer ${E}`,"Content-Type":"application/json"}});if(console.log("Response status:",m.status),console.log("Response headers:",m.headers.get("content-type")),!m.ok){if(m.status===401||m.status===403){console.error("认证失败，清除token并重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}const De=await m.text();console.error("API Error:",De);return}const $=m.headers.get("content-type");if(!$||!$.includes("application/json")){console.error("服务器返回非JSON响应，可能是认证问题"),Ce.logout(),t.push("/admin/login");return}const ie=await m.json();ie.success?k.value=ie.data:console.error("API返回错误:",ie.message)}catch(E){console.error("加载饮食偏好失败:",E),E instanceof TypeError&&E.message.includes("fetch")&&console.error("网络连接失败")}},ze=E=>{se.value=E,P.code=E.code,P.name=E.name,P.description=E.description||"",P.sortOrder=E.sortOrder,P.status=E.status,z.value=!0},at=async E=>{if(confirm(`确定要删除饮食偏好 "${E.name}" 吗？`))try{const m=rt.getToken();if(!m){console.error("Token缺失，重定向到管理员登录页面"),t.push("/admin/login");return}const $=await fetch(`http://localhost:8080/api/dietary-preferences/admin/${E.id}`,{method:"DELETE",headers:{Authorization:`Bearer ${m}`,"Content-Type":"application/json"}});if(!$.ok&&($.status===401||$.status===403)){console.error("认证失败，清除token并重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}(await $.json()).success&&Ne()}catch(m){console.error("删除饮食偏好失败:",m)}},Ze=()=>{z.value=!1,se.value=null,Object.assign(P,{code:"",name:"",description:"",sortOrder:1,status:1})},Qt=async()=>{if(!H.value){H.value=!0;try{const E=rt.getToken();if(!E){console.error("Token缺失，重定向到管理员登录页面"),t.push("/admin/login");return}const m=se.value?`http://localhost:8080/api/dietary-preferences/admin/${se.value.id}`:"http://localhost:8080/api/dietary-preferences/admin/create",$=se.value?"PUT":"POST",ie=await fetch(m,{method:$,headers:{"Content-Type":"application/json",Authorization:`Bearer ${E}`},body:JSON.stringify(P)});if(!ie.ok&&(ie.status===401||ie.status===403)){console.error("认证失败，清除token并重定向到登录页面"),Ce.logout(),t.push("/admin/login");return}(await ie.json()).success&&(Ze(),Ne())}catch(E){console.error("保存饮食偏好失败:",E)}finally{H.value=!1}}};return Ns(s,E=>{E==="health-goals"?Q():E==="dietary-preferences"&&Ne()}),(E,m)=>(b(),C("div",vh,[n("aside",gh,[m[30]||(m[30]=Le('<div class="sidebar-header" data-v-c748b2e1><div class="logo" data-v-c748b2e1><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-c748b2e1><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-c748b2e1></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-c748b2e1></circle></svg></div><h2 class="brand-name" data-v-c748b2e1>管理后台</h2></div>',1)),n("nav",mh,[(b(),C(ae,null,Ve(T,$=>n("a",{key:$.key,href:"#",class:pe(["nav-item",{active:s.value===$.key}]),onClick:ie=>s.value=$.key},[n("span",yh,S($.icon),1),n("span",bh,S($.label),1)],10,wh)),64))]),n("div",_h,[n("div",kh,[n("div",{onClick:V,style:{cursor:"pointer"}},[ue(Uo,{"avatar-url":fe(Vo)(r.value?.avatar),name:r.value?.realName,size:40,clickable:!1,"show-upload-overlay":!0},null,8,["avatar-url","name"])]),n("div",Ch,[n("div",xh,S(r.value?.realName),1),n("div",$h,S(r.value?.roleName),1)])]),n("button",{onClick:cs,class:"logout-btn"},m[29]||(m[29]=[n("span",null,"退出登录",-1)]))]),n("input",{ref_key:"avatarFileInput",ref:F,type:"file",accept:"image/jpeg,image/jpg,image/png,image/gif",style:{display:"none"},onChange:X},null,544)]),n("main",Sh,[n("header",Th,[n("div",Eh,[n("h1",Ph,S(Oe()),1)]),n("div",Ah,[n("span",Mh,S(o.value),1)])]),n("div",Lh,[s.value==="overview"?(b(),C("div",Oh,[n("div",Rh,[n("div",Ih,[m[32]||(m[32]=n("div",{class:"stat-icon"},"👥",-1)),n("div",Uh,[m[31]||(m[31]=n("h3",null,"管理员总数",-1)),n("p",Vh,S(A.adminCount),1)])]),n("div",jh,[m[34]||(m[34]=n("div",{class:"stat-icon"},"👤",-1)),n("div",Nh,[m[33]||(m[33]=n("h3",null,"用户总数",-1)),n("p",Dh,S(v.value.totalUsers),1),n("p",Bh,"启用: "+S(v.value.activeUsers)+" | 禁用: "+S(v.value.inactiveUsers),1)])]),n("div",Fh,[m[36]||(m[36]=n("div",{class:"stat-icon"},"📊",-1)),n("div",Hh,[m[35]||(m[35]=n("h3",null,"今日注册",-1)),n("p",zh,S(v.value.todayRegistered),1)])]),n("div",Kh,[m[38]||(m[38]=n("div",{class:"stat-icon"},"🔄",-1)),n("div",Gh,[m[37]||(m[37]=n("h3",null,"系统状态",-1)),n("p",Wh,S(A.systemStatus),1)])])]),n("div",qh,[m[40]||(m[40]=n("h3",null,"最近活动",-1)),n("div",Zh,[y.value.length===0?(b(),C("div",Jh,m[39]||(m[39]=[n("p",null,"暂无最近活动记录",-1)]))):(b(!0),C(ae,{key:1},Ve(y.value,$=>(b(),C("div",{key:$.id,class:"activity-item"},[n("div",Yh,S(be($.createTime)),1),n("div",Qh,S($.content),1)]))),128))])])])):s.value==="admins"?(b(),C("div",Xh,[n("div",ev,[n("table",tv,[m[41]||(m[41]=n("thead",null,[n("tr",null,[n("th",null,"编号"),n("th",null,"用户名"),n("th",null,"真实姓名"),n("th",null,"邮箱"),n("th",null,"角色"),n("th",null,"状态"),n("th",null,"创建时间"),n("th",null,"操作")])],-1)),n("tbody",null,[(b(!0),C(ae,null,Ve(i.value,$=>(b(),C("tr",{key:$.id},[n("td",null,S($.id),1),n("td",null,S($.username),1),n("td",null,S($.realName),1),n("td",null,S($.email),1),n("td",null,[n("span",sv,S($.roleName),1)]),n("td",null,[n("span",{class:pe(["status-badge",$.status===1?"active":"inactive"])},S($.statusName),3)]),n("td",null,S(Ae($.createTime)),1),n("td",null,[n("div",nv,[n("button",{onClick:ie=>Dt($),class:"action-btn toggle"},S($.status===1?"禁用":"启用"),9,ov),n("button",{onClick:ie=>Bt($),class:"action-btn delete"},"删除",8,rv)])])]))),128))])])])])):s.value==="features"?(b(),C("div",iv,[n("div",lv,[n("button",{onClick:m[0]||(m[0]=$=>c.value=!0),class:"create-btn"},m[42]||(m[42]=[n("span",null,"+ 新增核心功能",-1)]))]),n("div",av,[n("table",cv,[m[43]||(m[43]=n("thead",null,[n("tr",null,[n("th",null,"编号"),n("th",null,"标题"),n("th",null,"描述"),n("th",null,"亮点"),n("th",null,"排序"),n("th",null,"状态"),n("th",null,"创建时间"),n("th",null,"操作")])],-1)),n("tbody",null,[(b(!0),C(ae,null,Ve(l.value,$=>(b(),C("tr",{key:$.id},[n("td",null,S($.id),1),n("td",null,S($.title),1),n("td",uv,S($.description),1),n("td",null,[n("div",dv,[(b(!0),C(ae,null,Ve($.highlights,ie=>(b(),C("span",{key:ie,class:"highlight-tag"},S(ie),1))),128))])]),n("td",null,S($.sortOrder),1),n("td",null,[n("span",{class:pe(["status-badge",$.status===1?"active":"inactive"])},S($.statusName),3)]),n("td",null,S(Ae($.createTime)),1),n("td",null,[n("div",fv,[n("button",{onClick:ie=>tt($),class:"action-btn edit"},"编辑",8,pv),n("button",{onClick:ie=>G($),class:"action-btn toggle"},S($.status===1?"禁用":"启用"),9,hv),n("button",{onClick:ie=>Y($),class:"action-btn delete"},"删除",8,vv)])])]))),128))])])])])):s.value==="users"?(b(),C("div",gv,[n("div",mv,[n("div",wv,[te(n("input",{"onUpdate:modelValue":m[1]||(m[1]=$=>g.username=$),type:"text",placeholder:"搜索用户名...",class:"search-input",onKeyup:xn(x,["enter"])},null,544),[[de,g.username]]),te(n("input",{"onUpdate:modelValue":m[2]||(m[2]=$=>g.email=$),type:"text",placeholder:"搜索邮箱...",class:"search-input",onKeyup:xn(x,["enter"])},null,544),[[de,g.email]]),te(n("select",{"onUpdate:modelValue":m[3]||(m[3]=$=>g.status=$),class:"filter-select",onChange:x},m[44]||(m[44]=[n("option",{value:void 0},"全部状态",-1),n("option",{value:1},"启用",-1),n("option",{value:0},"禁用",-1)]),544),[[ns,g.status]]),n("button",{onClick:x,class:"search-btn"},"搜索"),n("button",{onClick:L,class:"reset-btn"},"重置")])]),n("div",yv,[n("table",bv,[m[45]||(m[45]=n("thead",null,[n("tr",null,[n("th",null,"编号"),n("th",null,"用户名"),n("th",null,"邮箱"),n("th",null,"状态"),n("th",null,"注册时间"),n("th",null,"操作")])],-1)),n("tbody",null,[(b(!0),C(ae,null,Ve(f.value,$=>(b(),C("tr",{key:$.id},[n("td",null,S($.id),1),n("td",null,S($.username),1),n("td",null,S($.email),1),n("td",null,[n("span",{class:pe(["status-badge",$.status===1?"active":"inactive"])},S($.statusName||($.status===1?"启用":"禁用")),3)]),n("td",null,S(Ae($.createTime)),1),n("td",null,[n("div",_v,[n("button",{onClick:ie=>p($),class:"action-btn toggle"},S($.status===1?"禁用":"启用"),9,kv),n("button",{onClick:ie=>h($),class:"action-btn delete"},"删除",8,Cv)])])]))),128))])])]),n("div",xv,[n("div",$v," 共 "+S(_.total)+" 条记录，第 "+S(_.page)+" / "+S(Math.ceil(_.total/_.size))+" 页 ",1),n("div",Sv,[te(n("select",{"onUpdate:modelValue":m[4]||(m[4]=$=>_.size=$),onChange:m[5]||(m[5]=$=>O(_.size)),class:"page-size-select"},m[46]||(m[46]=[n("option",{value:10},"10条/页",-1),n("option",{value:20},"20条/页",-1),n("option",{value:50},"50条/页",-1)]),544),[[ns,_.size]]),n("button",{onClick:m[6]||(m[6]=$=>R(_.page-1)),disabled:_.page<=1,class:"page-btn"}," 上一页 ",8,Tv),n("span",Ev,[(b(!0),C(ae,null,Ve(K(_.page,Math.ceil(_.total/_.size)),$=>(b(),C("button",{key:$,onClick:ie=>R($),class:pe(["page-number",{active:$===_.page}])},S($),11,Pv))),128))]),n("button",{onClick:m[7]||(m[7]=$=>R(_.page+1)),disabled:_.page>=Math.ceil(_.total/_.size),class:"page-btn"}," 下一页 ",8,Av)])])])):s.value==="dietary-preferences"?(b(),C("div",Mv,[n("div",Lv,[n("button",{onClick:m[8]||(m[8]=$=>z.value=!0),class:"create-btn"},m[47]||(m[47]=[n("span",null,"+ 新增饮食偏好",-1)]))]),n("div",Ov,[n("table",Rv,[m[48]||(m[48]=n("thead",null,[n("tr",null,[n("th",null,"编号"),n("th",null,"代码"),n("th",null,"名称"),n("th",null,"描述"),n("th",null,"排序"),n("th",null,"状态"),n("th",null,"创建时间"),n("th",null,"操作")])],-1)),n("tbody",null,[(b(!0),C(ae,null,Ve(k.value,$=>(b(),C("tr",{key:$.id},[n("td",null,S($.id),1),n("td",null,[n("code",null,S($.code),1)]),n("td",null,S($.name),1),n("td",null,S($.description||"-"),1),n("td",null,S($.sortOrder),1),n("td",null,[n("span",{class:pe(["status-badge",$.status===1?"active":"inactive"])},S($.statusName),3)]),n("td",null,S(Ae($.createTime)),1),n("td",null,[n("div",Iv,[n("button",{onClick:ie=>ze($),class:"action-btn edit"},"编辑",8,Uv),n("button",{onClick:ie=>at($),class:"action-btn delete"},"删除",8,Vv)])])]))),128))])])])])):s.value==="health-goals"?(b(),C("div",jv,[n("div",Nv,[n("button",{onClick:m[9]||(m[9]=$=>Z.value=!0),class:"create-btn"},m[49]||(m[49]=[n("span",null,"+ 新增健康目标",-1)]))]),n("div",Dv,[n("table",Bv,[m[50]||(m[50]=n("thead",null,[n("tr",null,[n("th",null,"编号"),n("th",null,"代码"),n("th",null,"名称"),n("th",null,"描述"),n("th",null,"排序"),n("th",null,"状态"),n("th",null,"创建时间"),n("th",null,"操作")])],-1)),n("tbody",null,[(b(!0),C(ae,null,Ve(B.value,$=>(b(),C("tr",{key:$.id},[n("td",null,S($.id),1),n("td",null,[n("code",null,S($.code),1)]),n("td",null,S($.name),1),n("td",null,S($.description||"-"),1),n("td",null,S($.sortOrder),1),n("td",null,[n("span",{class:pe(["status-badge",$.status===1?"active":"inactive"])},S($.statusName),3)]),n("td",null,S(Ae($.createTime)),1),n("td",null,[n("div",Fv,[n("button",{onClick:ie=>re($),class:"action-btn edit"},"编辑",8,Hv),n("button",{onClick:ie=>he($),class:"action-btn delete"},"删除",8,zv)])])]))),128))])])])])):(b(),C("div",Kv,[m[51]||(m[51]=n("div",{class:"placeholder-icon"},"🚧",-1)),m[52]||(m[52]=n("h3",null,"功能开发中",-1)),n("p",null,S(Oe())+"功能正在开发中，敬请期待！",1)]))])]),c.value?(b(),C("div",{key:0,class:"modal-overlay",onClick:M},[n("div",{class:"modal-content feature-modal",onClick:m[16]||(m[16]=kt(()=>{},["stop"]))},[n("div",Gv,[n("h3",null,S(a.value?"编辑核心功能":"新增核心功能"),1),n("button",{onClick:M,class:"close-btn"},"×")]),n("form",{onSubmit:kt(q,["prevent"]),class:"modal-form"},[n("div",Wv,[m[53]||(m[53]=n("label",null,"功能标题",-1)),te(n("input",{"onUpdate:modelValue":m[10]||(m[10]=$=>U.title=$),type:"text",required:""},null,512),[[de,U.title]])]),n("div",qv,[m[54]||(m[54]=n("label",null,"功能描述",-1)),te(n("textarea",{"onUpdate:modelValue":m[11]||(m[11]=$=>U.description=$),rows:"3",required:""},null,512),[[de,U.description]])]),n("div",Zv,[m[55]||(m[55]=n("label",null,"功能图标 (SVG)",-1)),te(n("textarea",{"onUpdate:modelValue":m[12]||(m[12]=$=>U.icon=$),rows:"4",placeholder:"请输入SVG图标代码",required:""},null,512),[[de,U.icon]])]),n("div",Jv,[m[56]||(m[56]=n("label",null,"功能亮点 (每行一个)",-1)),te(n("textarea",{"onUpdate:modelValue":m[13]||(m[13]=$=>d.value=$),rows:"3",placeholder:"请输入功能亮点，每行一个",required:""},null,512),[[de,d.value]])]),n("div",Yv,[n("div",Qv,[m[57]||(m[57]=n("label",null,"排序顺序",-1)),te(n("input",{"onUpdate:modelValue":m[14]||(m[14]=$=>U.sortOrder=$),type:"number",min:"1"},null,512),[[de,U.sortOrder,void 0,{number:!0}]])]),n("div",Xv,[m[58]||(m[58]=n("label",null,"状态",-1)),te(n("select",{"onUpdate:modelValue":m[15]||(m[15]=$=>U.status=$),required:""},[(b(),C(ae,null,Ve(w,$=>n("option",{key:$.value,value:$.value},S($.label),9,e0)),64))],512),[[ns,U.status]])])]),n("div",t0,[n("button",{type:"button",onClick:M,class:"cancel-btn"},"取消"),n("button",{type:"submit",class:"submit-btn",disabled:u.value},S(u.value?"提交中...":a.value?"更新":"创建"),9,s0)])],32)])])):J("",!0),Z.value?(b(),C("div",{key:1,class:"modal-overlay",onClick:we},[n("div",{class:"modal-content health-goal-modal",onClick:m[22]||(m[22]=kt(()=>{},["stop"]))},[n("div",n0,[n("h3",null,S(N.value?"编辑健康目标":"新增健康目标"),1),n("button",{onClick:we,class:"close-btn"},"×")]),n("form",{onSubmit:kt(ye,["prevent"]),class:"modal-form"},[n("div",o0,[m[59]||(m[59]=n("label",null,"目标代码",-1)),te(n("input",{"onUpdate:modelValue":m[17]||(m[17]=$=>I.code=$),type:"text",required:"",placeholder:"如：weight-loss"},null,512),[[de,I.code]])]),n("div",r0,[m[60]||(m[60]=n("label",null,"目标名称",-1)),te(n("input",{"onUpdate:modelValue":m[18]||(m[18]=$=>I.name=$),type:"text",required:"",placeholder:"如：减重"},null,512),[[de,I.name]])]),n("div",i0,[m[61]||(m[61]=n("label",null,"目标描述",-1)),te(n("textarea",{"onUpdate:modelValue":m[19]||(m[19]=$=>I.description=$),rows:"3",placeholder:"请输入目标描述"},null,512),[[de,I.description]])]),n("div",l0,[n("div",a0,[m[62]||(m[62]=n("label",null,"排序顺序",-1)),te(n("input",{"onUpdate:modelValue":m[20]||(m[20]=$=>I.sortOrder=$),type:"number",min:"1",required:""},null,512),[[de,I.sortOrder,void 0,{number:!0}]])]),n("div",c0,[m[64]||(m[64]=n("label",null,"状态",-1)),te(n("select",{"onUpdate:modelValue":m[21]||(m[21]=$=>I.status=$),required:""},m[63]||(m[63]=[n("option",{value:1},"启用",-1),n("option",{value:0},"禁用",-1)]),512),[[ns,I.status,void 0,{number:!0}]])])]),n("div",u0,[n("button",{type:"button",onClick:we,class:"cancel-btn"},"取消"),n("button",{type:"submit",class:"submit-btn",disabled:oe.value},S(oe.value?"提交中...":N.value?"更新":"创建"),9,d0)])],32)])])):J("",!0),z.value?(b(),C("div",{key:2,class:"modal-overlay",onClick:Ze},[n("div",{class:"modal-content dietary-preference-modal",onClick:m[28]||(m[28]=kt(()=>{},["stop"]))},[n("div",f0,[n("h3",null,S(se.value?"编辑饮食偏好":"新增饮食偏好"),1),n("button",{onClick:Ze,class:"close-btn"},"×")]),n("form",{onSubmit:kt(Qt,["prevent"]),class:"modal-form"},[n("div",p0,[m[65]||(m[65]=n("label",null,"偏好代码",-1)),te(n("input",{"onUpdate:modelValue":m[23]||(m[23]=$=>P.code=$),type:"text",required:"",placeholder:"如：vegetarian"},null,512),[[de,P.code]])]),n("div",h0,[m[66]||(m[66]=n("label",null,"偏好名称",-1)),te(n("input",{"onUpdate:modelValue":m[24]||(m[24]=$=>P.name=$),type:"text",required:"",placeholder:"如：素食主义"},null,512),[[de,P.name]])]),n("div",v0,[m[67]||(m[67]=n("label",null,"偏好描述",-1)),te(n("textarea",{"onUpdate:modelValue":m[25]||(m[25]=$=>P.description=$),rows:"3",placeholder:"请输入偏好描述"},null,512),[[de,P.description]])]),n("div",g0,[n("div",m0,[m[68]||(m[68]=n("label",null,"排序顺序",-1)),te(n("input",{"onUpdate:modelValue":m[26]||(m[26]=$=>P.sortOrder=$),type:"number",min:"1",required:""},null,512),[[de,P.sortOrder,void 0,{number:!0}]])]),n("div",w0,[m[70]||(m[70]=n("label",null,"状态",-1)),te(n("select",{"onUpdate:modelValue":m[27]||(m[27]=$=>P.status=$),required:""},m[69]||(m[69]=[n("option",{value:1},"启用",-1),n("option",{value:0},"禁用",-1)]),512),[[ns,P.status,void 0,{number:!0}]])])]),n("div",y0,[n("button",{type:"button",onClick:Ze,class:"cancel-btn"},"取消"),n("button",{type:"submit",class:"submit-btn",disabled:H.value},S(H.value?"提交中...":se.value?"更新":"创建"),9,b0)])],32)])])):J("",!0)]))}}),k0=Ue(_0,[["__scopeId","data-v-c748b2e1"]]),C0=(e,t)=>{const s=new URLSearchParams;e&&s.append("keyword",e);const o=`/api/foods/search${s.toString()?"?"+s.toString():""}`;return jn(o)},x0=()=>jn("/api/foods/categories"),$0=(e,t)=>jn(`/api/foods/${e}/analyze?weight=${t}`,{method:"POST"}),S0=(e=10)=>jn(`/api/foods/popular?limit=${e}`),ds=(e,t)=>e==null?"-":e===0?"0"+t:e<1?e.toFixed(2)+t:e<10?e.toFixed(1)+t:Math.round(e)+t,T0={class:"nutrition-container"},E0={class:"main-content"},P0={class:"search-section"},A0={class:"search-card"},M0={class:"search-container"},L0={class:"search-input-wrapper"},O0=["disabled"],R0={key:0,class:"search-suggestions"},I0=["onClick"],U0={class:"food-name"},V0={class:"food-category"},j0={key:0,class:"food-info-section"},N0={class:"food-info-card"},D0={class:"food-header"},B0={class:"section-title"},F0={class:"food-category-badge"},H0={class:"weight-input-section"},z0={class:"weight-input-wrapper"},K0={key:1,class:"popular-section"},G0={class:"popular-card"},W0={class:"popular-grid"},q0=["onClick"],Z0={class:"popular-name"},J0={class:"popular-category"},Y0={class:"popular-calories"},Q0={key:2,class:"nutrition-section"},X0={class:"nutrition-card"},e2={class:"nutrition-subtitle"},t2={key:0,class:"loading-state"},s2={class:"main-nutrients"},n2={class:"nutrient-item primary"},o2={class:"nutrient-info"},r2={class:"nutrient-value"},i2={class:"nutrient-item"},l2={class:"nutrient-info"},a2={class:"nutrient-value"},c2={class:"nutrient-item"},u2={class:"nutrient-info"},d2={class:"nutrient-value"},f2={class:"nutrient-item"},p2={class:"nutrient-info"},h2={class:"nutrient-value"},v2={class:"detailed-nutrients"},g2={class:"nutrient-grid"},m2={class:"nutrient-detail"},w2={class:"detail-value"},y2={class:"nutrient-detail"},b2={class:"detail-value"},_2={class:"nutrient-detail"},k2={class:"detail-value"},C2={class:"nutrient-detail"},x2={class:"detail-value"},$2={class:"nutrient-detail"},S2={class:"detail-value"},T2={class:"nutrient-detail"},E2={class:"detail-value"},P2=Ie({__name:"NutritionView",setup(e){lt();const t=j(""),s=j([]),o=j(null),r=j(100),i=j(!1),l=j(!1),c=j([]),a=j([]),u=j(null),d=async()=>{if(t.value.trim().length<2){s.value=[];return}try{i.value=!0;const w=await C0(t.value.trim());s.value=w.slice(0,5)}catch(w){console.error("搜索出错:",w),s.value=[]}finally{i.value=!1}},f=()=>{t.value.trim()&&d()},v=async w=>{o.value=w,t.value=w.name,s.value=[],await g()},g=async()=>{if(o.value)try{l.value=!0;const w=await $0(o.value.id,r.value);u.value=w}catch(w){console.error("营养分析出错:",w),u.value=null}finally{l.value=!1}},_=()=>{o.value=null,u.value=null,t.value="",s.value=[],r.value=100},y=async()=>{try{const w=await x0();c.value=w;const T=await S0(6);a.value=T}catch(w){console.error("初始化数据失败:",w)}};return jt(()=>{y()}),(w,T)=>(b(),C("div",T0,[ue(tn,{"page-title":"营养分析","back-to":"/dashboard","back-text":"返回控制台","show-user-info":!1}),T[30]||(T[30]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"}),n("div",{class:"circle circle-3"})],-1)),n("main",E0,[T[28]||(T[28]=n("section",{class:"page-header"},[n("div",{class:"header-content"},[n("h1",{class:"page-title"},"营养分析"),n("p",{class:"page-subtitle"},"分析食物营养成分，制定健康饮食计划")])],-1)),n("section",P0,[n("div",A0,[T[2]||(T[2]=n("h2",{class:"section-title"},[n("span",{class:"title-icon"},"🔍"),ee(" 食物搜索 ")],-1)),n("div",M0,[n("div",L0,[te(n("input",{"onUpdate:modelValue":T[0]||(T[0]=A=>t.value=A),type:"text",placeholder:"搜索食物名称，如：苹果、鸡蛋、牛奶...",class:"search-input",onInput:d,onKeyup:xn(f,["enter"])},null,544),[[de,t.value]]),n("button",{onClick:f,class:"search-btn",disabled:!t.value.trim()||i.value},S(i.value?"搜索中...":"搜索"),9,O0)]),s.value.length>0?(b(),C("div",R0,[(b(!0),C(ae,null,Ve(s.value,A=>(b(),C("div",{key:A.id,class:"suggestion-item",onClick:U=>v(A)},[n("span",U0,S(A.name),1),n("span",V0,S(A.category),1)],8,I0))),128))])):J("",!0)])])]),o.value?(b(),C("section",j0,[n("div",N0,[n("div",D0,[n("h2",B0,[T[3]||(T[3]=n("span",{class:"title-icon"},"🥗",-1)),ee(" "+S(o.value.name),1)]),n("span",F0,S(o.value.category),1)]),n("div",H0,[T[5]||(T[5]=n("label",{class:"weight-label"},"食用重量：",-1)),n("div",z0,[te(n("input",{"onUpdate:modelValue":T[1]||(T[1]=A=>r.value=A),type:"number",min:"1",max:"9999",class:"weight-input",onInput:g},null,544),[[de,r.value,void 0,{number:!0}]]),T[4]||(T[4]=n("span",{class:"weight-unit"},"克",-1))])])])])):J("",!0),!o.value&&a.value.length>0?(b(),C("section",K0,[n("div",G0,[T[6]||(T[6]=n("h2",{class:"section-title"},[n("span",{class:"title-icon"},"🔥"),ee(" 热门食物推荐 ")],-1)),n("div",W0,[(b(!0),C(ae,null,Ve(a.value,A=>(b(),C("div",{key:A.id,class:"popular-item",onClick:U=>v(A)},[n("div",Z0,S(A.name),1),n("div",J0,S(A.category),1),n("div",Y0,S(A.nutrition.calories)+"千卡/100g",1)],8,q0))),128))])])])):J("",!0),o.value?(b(),C("section",Q0,[n("div",X0,[n("div",{class:"nutrition-header"},[T[7]||(T[7]=n("h2",{class:"section-title"},[n("span",{class:"title-icon"},"📊"),ee(" 营养成分分析 ")],-1)),n("button",{onClick:_,class:"clear-btn"},"重新选择")]),n("p",e2,"基于 "+S(r.value)+"g "+S(o.value.name)+" 的营养成分",1),l.value?(b(),C("div",t2,T[8]||(T[8]=[n("div",{class:"loading-spinner"},null,-1),n("p",null,"正在分析营养成分...",-1)]))):J("",!0),n("div",s2,[n("div",n2,[T[11]||(T[11]=n("div",{class:"nutrient-icon"},"🔥",-1)),n("div",o2,[T[9]||(T[9]=n("span",{class:"nutrient-name"},"热量",-1)),n("span",r2,S(u.value?.calculatedNutrition.calories||0),1),T[10]||(T[10]=n("span",{class:"nutrient-unit"},"千卡",-1))])]),n("div",i2,[T[14]||(T[14]=n("div",{class:"nutrient-icon"},"🥩",-1)),n("div",l2,[T[12]||(T[12]=n("span",{class:"nutrient-name"},"蛋白质",-1)),n("span",a2,S(u.value?.calculatedNutrition.protein||0),1),T[13]||(T[13]=n("span",{class:"nutrient-unit"},"g",-1))])]),n("div",c2,[T[17]||(T[17]=n("div",{class:"nutrient-icon"},"🧈",-1)),n("div",u2,[T[15]||(T[15]=n("span",{class:"nutrient-name"},"脂肪",-1)),n("span",d2,S(u.value?.calculatedNutrition.fat||0),1),T[16]||(T[16]=n("span",{class:"nutrient-unit"},"g",-1))])]),n("div",f2,[T[20]||(T[20]=n("div",{class:"nutrient-icon"},"🍞",-1)),n("div",p2,[T[18]||(T[18]=n("span",{class:"nutrient-name"},"碳水化合物",-1)),n("span",h2,S(u.value?.calculatedNutrition.carbohydrates||0),1),T[19]||(T[19]=n("span",{class:"nutrient-unit"},"g",-1))])])]),n("div",v2,[T[27]||(T[27]=n("h3",{class:"detailed-title"},"详细营养成分",-1)),n("div",g2,[n("div",m2,[T[21]||(T[21]=n("span",{class:"detail-name"},"膳食纤维",-1)),n("span",w2,S(fe(ds)(u.value?.calculatedNutrition.fiber,"g")),1)]),n("div",y2,[T[22]||(T[22]=n("span",{class:"detail-name"},"糖分",-1)),n("span",b2,S(fe(ds)(u.value?.calculatedNutrition.sugar,"g")),1)]),n("div",_2,[T[23]||(T[23]=n("span",{class:"detail-name"},"钠",-1)),n("span",k2,S(fe(ds)(u.value?.calculatedNutrition.sodium,"mg")),1)]),n("div",C2,[T[24]||(T[24]=n("span",{class:"detail-name"},"钙",-1)),n("span",x2,S(fe(ds)(u.value?.calculatedNutrition.calcium,"mg")),1)]),n("div",$2,[T[25]||(T[25]=n("span",{class:"detail-name"},"铁",-1)),n("span",S2,S(fe(ds)(u.value?.calculatedNutrition.iron,"mg")),1)]),n("div",T2,[T[26]||(T[26]=n("span",{class:"detail-name"},"维生素C",-1)),n("span",E2,S(fe(ds)(u.value?.calculatedNutrition.vitaminC,"mg")),1)])])])])])):J("",!0),T[29]||(T[29]=Le('<section class="recommendation-section" data-v-5ea09e1e><div class="recommendation-card" data-v-5ea09e1e><h2 class="section-title" data-v-5ea09e1e><span class="title-icon" data-v-5ea09e1e>💡</span> 每日营养建议 </h2><div class="recommendation-grid" data-v-5ea09e1e><div class="recommendation-item" data-v-5ea09e1e><div class="rec-icon" data-v-5ea09e1e>🔥</div><div class="rec-content" data-v-5ea09e1e><h4 data-v-5ea09e1e>成人每日热量需求</h4><p data-v-5ea09e1e>男性：2000-2500千卡<br data-v-5ea09e1e>女性：1600-2000千卡</p></div></div><div class="recommendation-item" data-v-5ea09e1e><div class="rec-icon" data-v-5ea09e1e>🥩</div><div class="rec-content" data-v-5ea09e1e><h4 data-v-5ea09e1e>蛋白质建议摄入</h4><p data-v-5ea09e1e>每公斤体重需要0.8-1.2g蛋白质</p></div></div><div class="recommendation-item" data-v-5ea09e1e><div class="rec-icon" data-v-5ea09e1e>🥬</div><div class="rec-content" data-v-5ea09e1e><h4 data-v-5ea09e1e>膳食纤维建议</h4><p data-v-5ea09e1e>成人每日需要25-35g膳食纤维</p></div></div></div></div></section>',1))])]))}}),A2=Ue(P2,[["__scopeId","data-v-5ea09e1e"]]),M2={class:"page-container"},L2=Ie({__name:"MealsView",setup(e){return(t,s)=>(b(),C("div",M2,s[0]||(s[0]=[Le('<div class="page-header" data-v-8a3af552><h1 data-v-8a3af552>膳食记录</h1><p data-v-8a3af552>记录每日饮食，追踪营养摄入情况</p></div><div class="page-content" data-v-8a3af552><div class="placeholder-card" data-v-8a3af552><div class="placeholder-icon" data-v-8a3af552>🍽️</div><h2 data-v-8a3af552>膳食记录功能</h2><p data-v-8a3af552>此页面将包含以下功能：</p><ul data-v-8a3af552><li data-v-8a3af552>每日膳食记录</li><li data-v-8a3af552>食物摄入量统计</li><li data-v-8a3af552>膳食时间管理</li><li data-v-8a3af552>营养摄入追踪</li><li data-v-8a3af552>膳食历史查看</li></ul><div class="status-badge" data-v-8a3af552>开发中</div></div></div>',2)])))}}),O2=Ue(L2,[["__scopeId","data-v-8a3af552"]]),R2={class:"page-container"},I2=Ie({__name:"GoalsView",setup(e){return(t,s)=>(b(),C("div",R2,s[0]||(s[0]=[Le('<div class="page-header" data-v-db363767><h1 data-v-db363767>健康目标</h1><p data-v-db363767>设置和追踪健康目标</p></div><div class="page-content" data-v-db363767><div class="placeholder-card" data-v-db363767><div class="placeholder-icon" data-v-db363767>🎯</div><h2 data-v-db363767>健康目标功能</h2><p data-v-db363767>此页面将包含以下功能：</p><ul data-v-db363767><li data-v-db363767>每日卡路里目标设置</li><li data-v-db363767>营养素摄入目标</li><li data-v-db363767>体重管理目标</li><li data-v-db363767>运动目标设置</li><li data-v-db363767>目标进度追踪</li><li data-v-db363767>目标达成统计</li></ul><div class="status-badge" data-v-db363767>开发中</div></div></div>',2)])))}}),U2=Ue(I2,[["__scopeId","data-v-db363767"]]),V2={class:"page-container"},j2=Ie({__name:"ReportsView",setup(e){return(t,s)=>(b(),C("div",V2,s[0]||(s[0]=[Le('<div class="page-header" data-v-2a31eab3><h1 data-v-2a31eab3>健康报告</h1><p data-v-2a31eab3>查看详细的健康报告</p></div><div class="page-content" data-v-2a31eab3><div class="placeholder-card" data-v-2a31eab3><div class="placeholder-icon" data-v-2a31eab3>📊</div><h2 data-v-2a31eab3>健康报告功能</h2><p data-v-2a31eab3>此页面将包含以下功能：</p><ul data-v-2a31eab3><li data-v-2a31eab3>周营养摄入报告</li><li data-v-2a31eab3>月度健康评估</li><li data-v-2a31eab3>营养趋势分析</li><li data-v-2a31eab3>体重变化图表</li><li data-v-2a31eab3>目标达成报告</li><li data-v-2a31eab3>健康建议生成</li></ul><div class="status-badge" data-v-2a31eab3>开发中</div></div></div>',2)])))}}),N2=Ue(j2,[["__scopeId","data-v-2a31eab3"]]),D2={class:"profile-container"},B2=["disabled"],F2={key:0},H2={key:1},z2={class:"main-content"},K2={class:"profile-grid"},G2={class:"profile-card avatar-card"},W2={class:"avatar-section"},q2={class:"basic-info"},Z2={class:"form-group"},J2=["value"],Y2={class:"form-group"},Q2=["value"],X2={class:"form-group"},eg={class:"profile-card contact-card"},tg={class:"form-grid"},sg={class:"form-group"},ng={class:"form-group"},og={class:"form-group"},rg={class:"form-group"},ig={class:"profile-card body-card"},lg={class:"form-grid"},ag={class:"form-group"},cg={class:"form-group"},ug={key:0,class:"form-group bmi-display"},dg={class:"profile-card health-card"},fg={class:"form-group"},pg={class:"tag-input-container"},hg={class:"tags"},vg=["onClick"],gg={key:0,class:"tag-placeholder"},mg={class:"form-group"},wg={key:0,class:"loading-state"},yg={key:1,class:"empty-state"},bg={key:2,class:"checkbox-group"},_g=["value"],kg={class:"checkbox-label"},Cg={class:"form-group"},xg={key:0,class:"loading-state"},$g={key:1,class:"empty-state"},Sg={key:2,class:"checkbox-group"},Tg=["value"],Eg={class:"checkbox-label"},Pg={class:"profile-card bio-card"},Ag={class:"form-group"},Mg={class:"char-count"},Lg=Ie({__name:"ProfileView",setup(e){const t=lt(),{user:s,isLoggedIn:o,updateUserState:r}=Nt(),i=j(),l=j(null),c=j(!1),a=j(!1),u=Me({realName:"",phone:"",gender:null,birthday:"",age:null,height:null,weight:null,bio:""}),d=j(""),f=j([]),v=j([]),g=j([]),_=j(!1),y=j(!1),w=async()=>{_.value=!0;try{const H=await fetch("http://localhost:8080/api/dietary-preferences/enabled");if(!H.ok)throw new Error(`HTTP ${H.status}: ${H.statusText}`);const P=await H.json();P.success&&P.data?v.value=P.data.map(le=>({value:le.code,label:le.name})):(console.error("API返回错误:",P.message),$e.error("加载饮食偏好选项失败: "+(P.message||"未知错误")))}catch(H){console.error("加载饮食偏好选项失败:",H),H instanceof TypeError&&H.message.includes("fetch")?$e.error("网络连接失败，无法加载饮食偏好选项"):$e.error("加载饮食偏好选项失败，请稍后重试"),v.value=[]}finally{_.value=!1}},T=async()=>{y.value=!0;try{const H=await fetch("http://localhost:8080/api/health-goals/enabled");if(!H.ok)throw new Error(`HTTP ${H.status}: ${H.statusText}`);const P=await H.json();P.success&&P.data?g.value=P.data.map(le=>({value:le.code,label:le.name})):(console.error("API返回错误:",P.message),$e.error("加载健康目标选项失败: "+(P.message||"未知错误")))}catch(H){console.error("加载健康目标选项失败:",H),H instanceof TypeError&&H.message.includes("fetch")?$e.error("网络连接失败，无法加载健康目标选项"):$e.error("加载健康目标选项失败，请稍后重试"),g.value=[]}finally{y.value=!1}},A=j([]),U=j([]),B=Te(()=>{if(u.height&&u.weight){const H=u.height/100;return u.weight/(H*H)}return null}),Z=Te(()=>B.value?B.value<18.5?{text:"偏瘦",class:"underweight"}:B.value<24?{text:"正常",class:"normal"}:B.value<28?{text:"超重",class:"overweight"}:{text:"肥胖",class:"obese"}:{text:"",class:""}),N=async()=>{try{const H=await Vd();if(H.success&&H.data){if(l.value=H.data,Object.assign(u,{realName:H.data.realName||"",phone:H.data.phone||"",gender:H.data.gender,birthday:H.data.birthday||"",age:H.data.age,height:H.data.height,weight:H.data.weight,bio:H.data.bio||""}),H.data.allergies)try{f.value=JSON.parse(H.data.allergies)}catch{f.value=[]}else f.value=[];if(H.data.dietaryPreferences)try{A.value=JSON.parse(H.data.dietaryPreferences)}catch{A.value=[]}else A.value=[];if(H.data.healthGoals)try{U.value=JSON.parse(H.data.healthGoals)}catch{U.value=[]}else U.value=[]}else $e.error(H.message||"获取用户资料失败")}catch{$e.error("获取用户资料失败，请稍后重试")}},oe=()=>{const H=d.value.trim();H&&!f.value.includes(H)&&(f.value.push(H),d.value="")},I=H=>{f.value.splice(H,1)},k=()=>{a.value||i.value?.click()},z=async H=>{const P=H.target,le=P.files?.[0];if(!le){a.value=!1;return}if(le.size>10*1024*1024){$e.error("头像文件大小不能超过10MB"),a.value=!1;return}a.value=!0;try{const Oe=await ol(le);Oe.success?(s.value&&(s.value.avatar=Oe.data,je.setUser(s.value)),l.value&&(l.value.avatar=Oe.data),$e.success("头像上传成功"),r()):$e.error(Oe.message||"头像上传失败")}catch{$e.error("头像上传失败，请稍后重试")}finally{P.value="",a.value=!1}},se=async()=>{if(!c.value){c.value=!0;try{const H={...u,allergies:JSON.stringify(f.value),dietaryPreferences:JSON.stringify(A.value),healthGoals:JSON.stringify(U.value)},P=await jd(H);P.success&&P.data?(l.value=P.data,s.value&&(Object.assign(s.value,P.data),je.setUser(s.value)),$e.success("资料保存成功"),r()):$e.error(P.message||"资料保存失败")}catch{$e.error("资料保存失败，请稍后重试")}finally{c.value=!1}}};return jt(()=>{if(!o.value){t.push("/login");return}w(),T(),N()}),(H,P)=>(b(),C("div",D2,[ue(tn,{"page-title":"个人资料","back-to":"/dashboard","back-text":"返回仪表盘","show-user-info":!1},{actions:me(()=>[n("button",{onClick:se,disabled:c.value,class:"save-btn"},[c.value?(b(),C("span",F2,"保存中...")):(b(),C("span",H2,"保存"))],8,B2)]),_:1}),P[36]||(P[36]=n("div",{class:"background-decoration"},[n("div",{class:"circle circle-1"}),n("div",{class:"circle circle-2"})],-1)),n("main",z2,[n("div",K2,[n("div",G2,[P[15]||(P[15]=n("h2",{class:"card-title"},"头像和基本信息",-1)),n("div",W2,[n("div",{class:"avatar-container",onClick:k},[ue(Uo,{"avatar-url":fe(Vo)(fe(s)?.avatar),name:fe(s)?.username,size:120,clickable:!1,"show-upload-overlay":!0},null,8,["avatar-url","name"]),P[11]||(P[11]=n("div",{class:"avatar-overlay"},[n("span",{class:"upload-text"},"点击更换头像")],-1))]),n("div",q2,[n("div",Z2,[P[12]||(P[12]=n("label",null,"用户名",-1)),n("input",{type:"text",value:l.value?.username,disabled:"",class:"form-input disabled"},null,8,J2)]),n("div",Y2,[P[13]||(P[13]=n("label",null,"邮箱",-1)),n("input",{type:"email",value:l.value?.email,disabled:"",class:"form-input disabled"},null,8,Q2)]),n("div",X2,[P[14]||(P[14]=n("label",null,"真实姓名",-1)),te(n("input",{type:"text","onUpdate:modelValue":P[0]||(P[0]=le=>u.realName=le),placeholder:"请输入真实姓名",class:"form-input"},null,512),[[de,u.realName]])])])])]),n("div",eg,[P[21]||(P[21]=n("h2",{class:"card-title"},"联系信息",-1)),n("div",tg,[n("div",sg,[P[16]||(P[16]=n("label",null,"手机号码",-1)),te(n("input",{type:"tel","onUpdate:modelValue":P[1]||(P[1]=le=>u.phone=le),placeholder:"请输入手机号码",class:"form-input"},null,512),[[de,u.phone]])]),n("div",ng,[P[18]||(P[18]=n("label",null,"性别",-1)),te(n("select",{"onUpdate:modelValue":P[2]||(P[2]=le=>u.gender=le),class:"form-select"},P[17]||(P[17]=[n("option",{value:null},"请选择性别",-1),n("option",{value:1},"男",-1),n("option",{value:2},"女",-1),n("option",{value:0},"其他",-1)]),512),[[ns,u.gender]])]),n("div",og,[P[19]||(P[19]=n("label",null,"生日",-1)),te(n("input",{type:"date","onUpdate:modelValue":P[3]||(P[3]=le=>u.birthday=le),class:"form-input"},null,512),[[de,u.birthday]])]),n("div",rg,[P[20]||(P[20]=n("label",null,"年龄",-1)),te(n("input",{type:"number","onUpdate:modelValue":P[4]||(P[4]=le=>u.age=le),placeholder:"请输入年龄",min:"1",max:"150",class:"form-input"},null,512),[[de,u.age]])])])]),n("div",ig,[P[25]||(P[25]=n("h2",{class:"card-title"},"身体数据",-1)),n("div",lg,[n("div",ag,[P[22]||(P[22]=n("label",null,"身高 (cm)",-1)),te(n("input",{type:"number","onUpdate:modelValue":P[5]||(P[5]=le=>u.height=le),placeholder:"请输入身高",min:"50",max:"300",step:"0.1",class:"form-input"},null,512),[[de,u.height]])]),n("div",cg,[P[23]||(P[23]=n("label",null,"体重 (kg)",-1)),te(n("input",{type:"number","onUpdate:modelValue":P[6]||(P[6]=le=>u.weight=le),placeholder:"请输入体重",min:"20",max:"500",step:"0.1",class:"form-input"},null,512),[[de,u.weight]])]),B.value?(b(),C("div",ug,[P[24]||(P[24]=n("label",null,"BMI指数",-1)),n("div",{class:pe(["bmi-value",Z.value.class])},S(B.value.toFixed(1))+" - "+S(Z.value.text),3)])):J("",!0)])]),n("div",dg,[P[33]||(P[33]=n("h2",{class:"card-title"},"健康信息",-1)),n("div",fg,[P[26]||(P[26]=n("label",null,"过敏信息",-1)),n("div",pg,[n("div",hg,[(b(!0),C(ae,null,Ve(f.value,(le,Oe)=>(b(),C("span",{key:Oe,class:"tag"},[ee(S(le)+" ",1),n("button",{onClick:Ae=>I(Oe),class:"tag-remove"},"×",8,vg)]))),128)),f.value.length===0?(b(),C("span",gg," 暂无过敏信息 ")):J("",!0)]),te(n("input",{type:"text","onUpdate:modelValue":P[7]||(P[7]=le=>d.value=le),onKeydown:xn(oe,["enter"]),placeholder:"输入过敏信息后按回车添加",class:"form-input tag-input"},null,544),[[de,d.value]])])]),n("div",mg,[P[29]||(P[29]=n("label",null,"饮食偏好",-1)),_.value?(b(),C("div",wg,P[27]||(P[27]=[n("span",{class:"loading-text"},"正在加载饮食偏好选项...",-1)]))):v.value.length===0?(b(),C("div",yg,P[28]||(P[28]=[n("span",{class:"empty-text"},"暂无可用的饮食偏好选项",-1)]))):(b(),C("div",bg,[(b(!0),C(ae,null,Ve(v.value,le=>(b(),C("label",{class:"checkbox-item",key:le.value},[te(n("input",{type:"checkbox",value:le.value,"onUpdate:modelValue":P[8]||(P[8]=Oe=>A.value=Oe)},null,8,_g),[[Zs,A.value]]),n("span",kg,S(le.label),1)]))),128))]))]),n("div",Cg,[P[32]||(P[32]=n("label",null,"健康目标",-1)),y.value?(b(),C("div",xg,P[30]||(P[30]=[n("span",{class:"loading-text"},"正在加载健康目标选项...",-1)]))):g.value.length===0?(b(),C("div",$g,P[31]||(P[31]=[n("span",{class:"empty-text"},"暂无可用的健康目标选项",-1)]))):(b(),C("div",Sg,[(b(!0),C(ae,null,Ve(g.value,le=>(b(),C("label",{class:"checkbox-item",key:le.value},[te(n("input",{type:"checkbox",value:le.value,"onUpdate:modelValue":P[9]||(P[9]=Oe=>U.value=Oe)},null,8,Tg),[[Zs,U.value]]),n("span",Eg,S(le.label),1)]))),128))]))])]),n("div",Pg,[P[35]||(P[35]=n("h2",{class:"card-title"},"个人简介",-1)),n("div",Ag,[P[34]||(P[34]=n("label",null,"关于我",-1)),te(n("textarea",{"onUpdate:modelValue":P[10]||(P[10]=le=>u.bio=le),placeholder:"介绍一下自己...",rows:"4",maxlength:"500",class:"form-textarea"},null,512),[[de,u.bio]]),n("div",Mg,S((u.bio||"").length)+"/500",1)])])])]),n("input",{ref_key:"avatarFileInput",ref:i,type:"file",accept:"image/jpeg,image/jpg,image/png,image/gif",style:{display:"none"},onChange:z},null,544)]))}}),Og=Ue(Lg,[["__scopeId","data-v-c8180c81"]]),Rg={class:"page-container"},Ig=Ie({__name:"SettingsView",setup(e){return(t,s)=>(b(),C("div",Rg,s[0]||(s[0]=[Le('<div class="page-header" data-v-7101c29a><h1 data-v-7101c29a>设置</h1><p data-v-7101c29a>个性化设置</p></div><div class="page-content" data-v-7101c29a><div class="placeholder-card" data-v-7101c29a><div class="placeholder-icon" data-v-7101c29a>⚙️</div><h2 data-v-7101c29a>设置功能</h2><p data-v-7101c29a>此页面将包含以下功能：</p><ul data-v-7101c29a><li data-v-7101c29a>通知设置</li><li data-v-7101c29a>隐私设置</li><li data-v-7101c29a>数据导入导出</li><li data-v-7101c29a>主题设置</li><li data-v-7101c29a>语言设置</li><li data-v-7101c29a>账户安全</li></ul><div class="status-badge" data-v-7101c29a>开发中</div></div></div>',2)])))}}),Ug=Ue(Ig,[["__scopeId","data-v-7101c29a"]]),Vg={class:"error-container"},jg={class:"top-navbar"},Ng={class:"nav-content"},Dg={class:"nav-actions"},Bg={class:"welcome-text"},Fg={class:"error-main"},Hg={class:"error-content"},zg={class:"error-actions"},Kg=Ie({__name:"Error404View",setup(e){const t=lt(),{user:s,isLoggedIn:o,logout:r,updateUserState:i}=Nt(),l=()=>{window.history.length>1?t.go(-1):t.push("/")},c=()=>{window.location.reload()},a=()=>{r(),i(),t.push("/")};return(u,d)=>{const f=ft("router-link");return b(),C("div",Vg,[n("nav",jg,[n("div",Ng,[d[2]||(d[2]=Le('<div class="nav-brand" data-v-5924c40f><div class="brand-logo" data-v-5924c40f><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-5924c40f><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-5924c40f></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-5924c40f></circle></svg></div><span class="brand-name" data-v-5924c40f>膳食营养分析平台</span></div>',1)),n("div",Dg,[ue(f,{to:"/",class:"nav-btn home-btn"},{default:me(()=>d[0]||(d[0]=[ee("返回首页",-1)])),_:1,__:[0]}),fe(o)?(b(),C(ae,{key:0},[n("span",Bg,"欢迎，"+S(fe(s)?.username),1),n("button",{onClick:a,class:"nav-btn logout-btn"},"退出登录")],64)):(b(),Xe(f,{key:1,to:"/login",class:"nav-btn login-btn"},{default:me(()=>d[1]||(d[1]=[ee("登录",-1)])),_:1,__:[1]}))])])]),n("main",Fg,[n("div",Hg,[d[6]||(d[6]=Le('<div class="error-icon" data-v-5924c40f><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-5924c40f><text x="100" y="80" text-anchor="middle" class="error-number" data-v-5924c40f>404</text><circle cx="80" cy="130" r="15" stroke="currentColor" stroke-width="3" fill="none" data-v-5924c40f></circle><line x1="91" y1="141" x2="105" y2="155" stroke="currentColor" stroke-width="3" data-v-5924c40f></line><circle cx="130" cy="130" r="20" stroke="currentColor" stroke-width="3" fill="none" data-v-5924c40f></circle><path d="M125 125 Q130 120 135 125 Q135 130 130 135" stroke="currentColor" stroke-width="2" fill="none" data-v-5924c40f></path><circle cx="130" cy="145" r="2" fill="currentColor" data-v-5924c40f></circle></svg></div><div class="error-info" data-v-5924c40f><h1 class="error-title" data-v-5924c40f>页面未找到</h1><p class="error-description" data-v-5924c40f> 抱歉，您访问的页面不存在或已被移动。<br data-v-5924c40f> 请检查网址是否正确，或返回首页继续浏览。 </p><div class="error-reasons" data-v-5924c40f><h3 data-v-5924c40f>可能的原因：</h3><ul data-v-5924c40f><li data-v-5924c40f>网址输入错误</li><li data-v-5924c40f>页面已被删除或移动</li><li data-v-5924c40f>链接已过期</li><li data-v-5924c40f>您没有访问权限</li></ul></div></div>',2)),n("div",zg,[ue(f,{to:"/",class:"action-btn primary"},{default:me(()=>d[3]||(d[3]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 返回首页 ",-1)])),_:1,__:[3]}),n("button",{onClick:l,class:"action-btn secondary"},d[4]||(d[4]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 返回上页 ",-1)])),n("button",{onClick:c,class:"action-btn tertiary"},d[5]||(d[5]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 刷新页面 ",-1)]))]),d[7]||(d[7]=n("div",{class:"help-info"},[n("p",null,"如果问题持续存在，请联系我们的技术支持团队"),n("a",{href:"mailto:<EMAIL>",class:"help-link"},"<EMAIL>")],-1))])])])}}}),Gg=Ue(Kg,[["__scopeId","data-v-5924c40f"]]),Wg={class:"error-container"},qg={class:"top-navbar"},Zg={class:"nav-content"},Jg={class:"nav-actions"},Yg={class:"welcome-text"},Qg={class:"error-main"},Xg={class:"error-content"},em={class:"error-info"},tm={class:"error-description"},sm={class:"permission-info"},nm={key:0,class:"permission-section"},om={key:1,class:"permission-section"},rm={key:2,class:"permission-section"},im={class:"error-actions"},lm=Ie({__name:"Error403View",setup(e){const t=lt(),{user:s,isLoggedIn:o,logout:r,updateUserState:i}=Nt(),l=Te(()=>je.isUser()),c=Te(()=>je.isAdmin()),a=()=>{if(o.value){if(l.value)return"此页面需要管理员权限，您当前是普通用户。";if(c.value)return"此页面仅限普通用户访问，请使用管理后台。"}else return"请先登录您的账户以获取相应权限。";return"您的账户权限不足以访问此页面。"},u=()=>c.value?"/admin/dashboard":l.value?"/dashboard":"/",d=()=>c.value?"前往管理后台":l.value?"前往用户控制台":"返回首页",f=()=>{window.history.length>1?t.go(-1):t.push("/")},v=()=>{r(),i(),t.push("/")};return(g,_)=>{const y=ft("router-link");return b(),C("div",Wg,[n("nav",qg,[n("div",Zg,[_[2]||(_[2]=Le('<div class="nav-brand" data-v-de589a11><div class="brand-logo" data-v-de589a11><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-de589a11><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-de589a11></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-de589a11></circle></svg></div><span class="brand-name" data-v-de589a11>膳食营养分析平台</span></div>',1)),n("div",Jg,[ue(y,{to:"/",class:"nav-btn home-btn"},{default:me(()=>_[0]||(_[0]=[ee("返回首页",-1)])),_:1,__:[0]}),fe(o)?(b(),C(ae,{key:0},[n("span",Yg,"欢迎，"+S(fe(s)?.username),1),n("button",{onClick:v,class:"nav-btn logout-btn"},"退出登录")],64)):(b(),Xe(y,{key:1,to:"/login",class:"nav-btn login-btn"},{default:me(()=>_[1]||(_[1]=[ee("登录",-1)])),_:1,__:[1]}))])])]),n("main",Qg,[n("div",Xg,[_[14]||(_[14]=Le('<div class="error-icon" data-v-de589a11><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-de589a11><text x="100" y="80" text-anchor="middle" class="error-number" data-v-de589a11>403</text><rect x="75" y="120" width="50" height="35" rx="5" stroke="currentColor" stroke-width="3" fill="none" data-v-de589a11></rect><path d="M85 120 V110 C85 105 90 100 100 100 C110 100 115 105 115 110 V120" stroke="currentColor" stroke-width="3" fill="none" data-v-de589a11></path><circle cx="100" cy="135" r="3" fill="currentColor" data-v-de589a11></circle><circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="4" fill="none" opacity="0.3" data-v-de589a11></circle><line x1="50" y1="50" x2="150" y2="150" stroke="currentColor" stroke-width="4" opacity="0.3" data-v-de589a11></line></svg></div>',1)),n("div",em,[_[9]||(_[9]=n("h1",{class:"error-title"},"访问被拒绝",-1)),n("p",tm,[_[3]||(_[3]=ee(" 抱歉，您没有权限访问此页面。",-1)),_[4]||(_[4]=n("br",null,null,-1)),ee(" "+S(a()),1)]),n("div",sm,[_[8]||(_[8]=n("h3",null,"权限说明：",-1)),fe(o)?l.value?(b(),C("div",om,_[6]||(_[6]=[n("h4",null,"👤 普通用户权限",-1),n("p",null,"此页面仅限管理员访问，普通用户无法进入",-1)]))):c.value?(b(),C("div",rm,_[7]||(_[7]=[n("h4",null,"👨‍💼 管理员权限",-1),n("p",null,"此页面仅限普通用户访问，管理员请使用管理后台",-1)]))):J("",!0):(b(),C("div",nm,_[5]||(_[5]=[n("h4",null,"🔐 未登录用户",-1),n("p",null,"您需要登录后才能访问此页面",-1)])))])]),n("div",im,[fe(o)?(b(),Xe(y,{key:1,to:u(),class:"action-btn primary"},{default:me(()=>[_[12]||(_[12]=n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1)),ee(" "+S(d()),1)]),_:1,__:[12]},8,["to"])):(b(),C(ae,{key:0},[ue(y,{to:"/login",class:"action-btn primary"},{default:me(()=>_[10]||(_[10]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M15 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M10 17L15 12L10 7M15 12H3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 用户登录 ",-1)])),_:1,__:[10]}),ue(y,{to:"/admin/login",class:"action-btn secondary"},{default:me(()=>_[11]||(_[11]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M12 15C13.6569 15 15 13.6569 15 12C15 10.3431 13.6569 9 12 9C10.3431 9 9 10.3431 9 12C9 13.6569 10.3431 15 12 15Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M19.4 15C19.2669 15.3016 19.2272 15.6362 19.286 15.9606C19.3448 16.285 19.4995 16.5843 19.73 16.82L19.79 16.88C19.976 17.0657 20.1235 17.2863 20.2241 17.5291C20.3248 17.7719 20.3766 18.0322 20.3766 18.295C20.3766 18.5578 20.3248 18.8181 20.2241 19.0609C20.1235 19.3037 19.976 19.5243 19.79 19.71C19.6043 19.896 19.3837 20.0435 19.1409 20.1441C18.8981 20.2448 18.6378 20.2966 18.375 20.2966C18.1122 20.2966 17.8519 20.2448 17.6091 20.1441C17.3663 20.0435 17.1457 19.896 16.96 19.71L16.9 19.65C16.6643 19.4195 16.365 19.2648 16.0406 19.206C15.7162 19.1472 15.3816 19.1869 15.08 19.32C14.7842 19.4468 14.532 19.6572 14.3543 19.9255C14.1766 20.1938 14.0813 20.5082 14.08 20.83V21C14.08 21.5304 13.8693 22.0391 13.4942 22.4142C13.1191 22.7893 12.6104 23 12.08 23C11.5496 23 11.0409 22.7893 10.6658 22.4142C10.2907 22.0391 10.08 21.5304 10.08 21V20.91C10.0723 20.579 9.96512 20.258 9.77251 19.9887C9.5799 19.7194 9.31074 19.5143 9 19.4C8.69838 19.2669 8.36381 19.2272 8.03941 19.286C7.71502 19.3448 7.41568 19.4995 7.18 19.73L7.12 19.79C6.93425 19.976 6.71368 20.1235 6.47088 20.2241C6.22808 20.3248 5.96783 20.3766 5.705 20.3766C5.44217 20.3766 5.18192 20.3248 4.93912 20.2241C4.69632 20.1235 4.47575 19.976 4.29 19.79C4.10405 19.6043 3.95653 19.3837 3.85588 19.1409C3.75523 18.8981 3.70343 18.6378 3.70343 18.375C3.70343 18.1122 3.75523 17.8519 3.85588 17.6091C3.95653 17.3663 4.10405 17.1457 4.29 16.96L4.35 16.9C4.58054 16.6643 4.73519 16.365 4.794 16.0406C4.85282 15.7162 4.81312 15.3816 4.68 15.08C4.55324 14.7842 4.34276 14.532 4.07447 14.3543C3.80618 14.1766 3.49179 14.0813 3.17 14.08H3C2.46957 14.08 1.96086 13.8693 1.58579 13.4942C1.21071 13.1191 1 12.6104 1 12.08C1 11.5496 1.21071 11.0409 1.58579 10.6658C1.96086 10.2907 2.46957 10.08 3 10.08H3.09C3.42099 10.0723 3.742 9.96512 4.0113 9.77251C4.28059 9.5799 4.48572 9.31074 4.6 9C4.73312 8.69838 4.77282 8.36381 4.714 8.03941C4.65519 7.71502 4.50054 7.41568 4.27 7.18L4.21 7.12C4.02405 6.93425 3.87653 6.71368 3.77588 6.47088C3.67523 6.22808 3.62343 5.96783 3.62343 5.705C3.62343 5.44217 3.67523 5.18192 3.77588 4.93912C3.87653 4.69632 4.02405 4.47575 4.21 4.29C4.39575 4.10405 4.61632 3.95653 4.85912 3.85588C5.10192 3.75523 5.36217 3.70343 5.625 3.70343C5.88783 3.70343 6.14808 3.75523 6.39088 3.85588C6.63368 3.95653 6.85425 4.10405 7.04 4.29L7.1 4.35C7.33568 4.58054 7.63502 4.73519 7.95941 4.794C8.28381 4.85282 8.61838 4.81312 8.92 4.68H9C9.29577 4.55324 9.54802 4.34276 9.72569 4.07447C9.90337 3.80618 9.99872 3.49179 10 3.17V3C10 2.46957 10.2107 1.96086 10.5858 1.58579C10.9609 1.21071 11.4696 1 12 1C12.5304 1 13.0391 1.21071 13.4142 1.58579C13.7893 1.96086 14 2.46957 14 3V3.09C14.0013 3.41179 14.0966 3.72618 14.2743 3.99447C14.452 4.26276 14.7042 4.47324 15 4.6C15.3016 4.73312 15.6362 4.77282 15.9606 4.714C16.285 4.65519 16.5843 4.50054 16.82 4.27L16.88 4.21C17.0657 4.02405 17.2863 3.87653 17.5291 3.77588C17.7719 3.67523 18.0322 3.62343 18.295 3.62343C18.5578 3.62343 18.8181 3.67523 19.0609 3.77588C19.3037 3.87653 19.5243 4.02405 19.71 4.21C19.896 4.39575 20.0435 4.61632 20.1441 4.85912C20.2448 5.10192 20.2966 5.36217 20.2966 5.625C20.2966 5.88783 20.2448 6.14808 20.1441 6.39088C20.0435 6.63368 19.896 6.85425 19.71 7.04L19.65 7.1C19.4195 7.33568 19.2648 7.63502 19.206 7.95941C19.1472 8.28381 19.1869 8.61838 19.32 8.92V9C19.4468 9.29577 19.6572 9.54802 19.9255 9.72569C20.1938 9.90337 20.5082 9.99872 20.83 10H21C21.5304 10 22.0391 10.2107 22.4142 10.5858C22.7893 10.9609 23 11.4696 23 12C23 12.5304 22.7893 13.0391 22.4142 13.4142C22.0391 13.7893 21.5304 14 21 14H20.91C20.5882 14.0013 20.2738 14.0966 20.0055 14.2743C19.7372 14.452 19.5268 14.7042 19.4 15Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 管理员登录 ",-1)])),_:1,__:[11]})],64)),n("button",{onClick:f,class:"action-btn secondary"},_[13]||(_[13]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M19 12H5M12 19L5 12L12 5",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 返回上页 ",-1)]))]),_[15]||(_[15]=n("div",{class:"help-info"},[n("p",null,"如果您认为这是一个错误，请联系系统管理员"),n("a",{href:"mailto:<EMAIL>",class:"help-link"},"<EMAIL>")],-1))])])])}}}),am=Ue(lm,[["__scopeId","data-v-de589a11"]]),cm={class:"error-container"},um={class:"top-navbar"},dm={class:"nav-content"},fm={class:"nav-actions"},pm={class:"welcome-text"},hm={class:"error-main"},vm={class:"error-content"},gm={class:"error-info"},mm={class:"error-details"},wm={class:"error-code"},ym={class:"code-value"},bm={class:"error-time"},_m={class:"time-value"},km={key:0,class:"error-id"},Cm={class:"id-value"},xm={class:"error-actions"},$m=Ie({__name:"Error500View",setup(e){const t=lt(),{user:s,isLoggedIn:o,logout:r,updateUserState:i}=Nt(),l=j("HTTP 500"),c=j(""),a=j("");jt(()=>{c.value=new Date().toLocaleString("zh-CN"),a.value=u()});const u=()=>{const _=Date.now().toString(36),y=Math.random().toString(36).substr(2,5);return`ERR-${_}-${y}`.toUpperCase()},d=()=>{window.location.reload()},f=()=>{const _={errorCode:l.value,errorTime:c.value,errorId:a.value,userAgent:navigator.userAgent,url:window.location.href,userId:s.value?.id||"anonymous"};console.log("Error Report:",_),alert("感谢您的反馈！错误报告已提交，我们会尽快处理。")},v=()=>{alert("在线客服功能即将开放，请暂时使用邮箱或电话联系我们。")},g=()=>{r(),i(),t.push("/")};return(_,y)=>{const w=ft("router-link");return b(),C("div",cm,[n("nav",um,[n("div",dm,[y[2]||(y[2]=Le('<div class="nav-brand" data-v-4f26ff27><div class="brand-logo" data-v-4f26ff27><svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-4f26ff27><path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="currentColor" data-v-4f26ff27></path><circle cx="12" cy="12" r="3" fill="none" stroke="currentColor" stroke-width="2" data-v-4f26ff27></circle></svg></div><span class="brand-name" data-v-4f26ff27>膳食营养分析平台</span></div>',1)),n("div",fm,[ue(w,{to:"/",class:"nav-btn home-btn"},{default:me(()=>y[0]||(y[0]=[ee("返回首页",-1)])),_:1,__:[0]}),fe(o)?(b(),C(ae,{key:0},[n("span",pm,"欢迎，"+S(fe(s)?.username),1),n("button",{onClick:g,class:"nav-btn logout-btn"},"退出登录")],64)):(b(),Xe(w,{key:1,to:"/login",class:"nav-btn login-btn"},{default:me(()=>y[1]||(y[1]=[ee("登录",-1)])),_:1,__:[1]}))])])]),n("main",hm,[n("div",vm,[y[16]||(y[16]=Le('<div class="error-icon" data-v-4f26ff27><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-4f26ff27><text x="100" y="80" text-anchor="middle" class="error-number" data-v-4f26ff27>500</text><rect x="70" y="110" width="60" height="40" rx="4" stroke="currentColor" stroke-width="3" fill="none" data-v-4f26ff27></rect><rect x="75" y="115" width="50" height="30" rx="2" stroke="currentColor" stroke-width="2" fill="none" data-v-4f26ff27></rect><circle cx="85" cy="125" r="3" fill="#e74c3c" data-v-4f26ff27></circle><circle cx="95" cy="125" r="3" fill="#f39c12" data-v-4f26ff27></circle><circle cx="105" cy="125" r="3" fill="#e74c3c" data-v-4f26ff27></circle><line x1="110" y1="130" x2="120" y2="140" stroke="currentColor" stroke-width="2" data-v-4f26ff27></line><line x1="120" y1="130" x2="110" y2="140" stroke="currentColor" stroke-width="2" data-v-4f26ff27></line><rect x="75" y="150" width="50" height="8" rx="2" stroke="currentColor" stroke-width="2" fill="currentColor" opacity="0.3" data-v-4f26ff27></rect></svg></div>',1)),n("div",gm,[y[7]||(y[7]=n("h1",{class:"error-title"},"服务器内部错误",-1)),y[8]||(y[8]=n("p",{class:"error-description"},[ee(" 抱歉，服务器遇到了一个意外错误，无法完成您的请求。"),n("br"),ee(" 我们的技术团队已经收到通知，正在紧急处理此问题。 ")],-1)),n("div",mm,[y[6]||(y[6]=n("h3",null,"错误信息：",-1)),n("div",wm,[y[3]||(y[3]=n("span",{class:"code-label"},"错误代码:",-1)),n("span",ym,S(l.value),1)]),n("div",bm,[y[4]||(y[4]=n("span",{class:"time-label"},"发生时间:",-1)),n("span",_m,S(c.value),1)]),a.value?(b(),C("div",km,[y[5]||(y[5]=n("span",{class:"id-label"},"错误ID:",-1)),n("span",Cm,S(a.value),1)])):J("",!0)]),y[9]||(y[9]=n("div",{class:"error-solutions"},[n("h3",null,"您可以尝试："),n("ul",null,[n("li",null,"刷新页面重新加载"),n("li",null,"稍后再试（服务器可能正在维护）"),n("li",null,"清除浏览器缓存和Cookie"),n("li",null,"检查网络连接是否正常"),n("li",null,"联系技术支持获取帮助")])],-1))]),n("div",xm,[n("button",{onClick:d,class:"action-btn primary"},y[10]||(y[10]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 刷新页面 ",-1)])),ue(w,{to:"/",class:"action-btn secondary"},{default:me(()=>y[11]||(y[11]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 返回首页 ",-1)])),_:1,__:[11]}),n("button",{onClick:f,class:"action-btn tertiary"},y[12]||(y[12]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M21 15C21 15.5304 20.7893 16.0391 20.4142 16.4142C20.0391 16.7893 19.5304 17 19 17H7L3 21V5C3 4.46957 3.21071 3.96086 3.58579 3.58579C3.96086 3.21071 4.46957 3 5 3H19C19.5304 3 20.0391 3.21071 20.4142 3.58579C20.7893 3.96086 21 4.46957 21 5V15Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M12 7V13M12 17H12.01",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 报告错误 ",-1)]))]),n("div",{class:"contact-info"},[y[15]||(y[15]=n("h3",null,"需要帮助？",-1)),n("div",{class:"contact-methods"},[y[14]||(y[14]=Le('<div class="contact-item" data-v-4f26ff27><span class="contact-label" data-v-4f26ff27>技术支持邮箱:</span><a href="mailto:<EMAIL>" class="contact-link" data-v-4f26ff27><EMAIL></a></div><div class="contact-item" data-v-4f26ff27><span class="contact-label" data-v-4f26ff27>客服热线:</span><a href="tel:************" class="contact-link" data-v-4f26ff27>************</a></div>',2)),n("div",{class:"contact-item"},[y[13]||(y[13]=n("span",{class:"contact-label"},"在线客服:",-1)),n("button",{onClick:v,class:"contact-link-btn"},"点击咨询")])])])])])])}}}),Sm=Ue($m,[["__scopeId","data-v-4f26ff27"]]),Tm={class:"error-container"},Em={class:"error-main"},Pm={class:"error-content"},Am={class:"error-info"},Mm={class:"error-description"},Lm={class:"network-status"},Om={class:"status-item"},Rm={class:"status-item"},Im={class:"status-value"},Um={class:"status-item"},Vm={key:0,class:"status-item"},jm={class:"status-value"},Nm={class:"error-actions"},Dm=["disabled"],Bm={key:0,class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Fm={key:1,class:"btn-icon spinning",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Hm=["disabled"],zm={key:0,class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Km={key:1,class:"btn-icon spinning",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},Gm=Ie({__name:"ErrorNetworkView",setup(e){const t=lt(),{logout:s,updateUserState:o}=Nt(),r=j(!1),i=j(!1),l=Me({online:navigator.onLine,type:a()}),c=Me({reachable:!1,responseTime:0});function a(){const y=navigator.connection||navigator.mozConnection||navigator.webkitConnection;return y?y.effectiveType||y.type||"未知":navigator.onLine?"已连接":"离线"}const u=()=>{if(l.online){if(!c.reachable)return"网络连接正常，但无法访问服务器。"}else return"您的设备似乎已断开网络连接。";return"网络连接出现问题，请稍后重试。"},d=async()=>{r.value=!0;try{l.online=navigator.onLine,l.type=a();const y=Date.now();try{const w=await fetch("/api/health",{method:"GET",cache:"no-cache",signal:AbortSignal.timeout(5e3)}),T=Date.now();c.responseTime=T-y,c.reachable=w.ok}catch{c.reachable=!1,c.responseTime=Date.now()-y}await new Promise(w=>setTimeout(w,1e3))}finally{r.value=!1}},f=async()=>{i.value=!0;try{await d(),l.online&&c.reachable&&window.location.reload()}finally{i.value=!1}},v=()=>{l.online=!0,l.type=a()},g=()=>{l.online=!1,l.type="离线"},_=()=>{s(),o(),t.push("/")};return jt(()=>{window.addEventListener("online",v),window.addEventListener("offline",g),d()}),Ss(()=>{window.removeEventListener("online",v),window.removeEventListener("offline",g)}),(y,w)=>{const T=ft("router-link");return b(),C("div",Tm,[ue(tn,{"show-logout-button":!0,onLogout:_},{actions:me(()=>[ue(T,{to:"/",class:"nav-btn home-btn"},{default:me(()=>w[0]||(w[0]=[ee("返回首页",-1)])),_:1,__:[0]})]),_:1}),n("main",Em,[n("div",Pm,[w[15]||(w[15]=Le('<div class="error-icon" data-v-5e1c3535><svg viewBox="0 0 200 200" fill="none" xmlns="http://www.w3.org/2000/svg" data-v-5e1c3535><circle cx="100" cy="100" r="80" stroke="currentColor" stroke-width="3" fill="none" opacity="0.3" data-v-5e1c3535></circle><path d="M60 120 Q100 80 140 120" stroke="currentColor" stroke-width="4" fill="none" opacity="0.5" data-v-5e1c3535></path><path d="M70 130 Q100 100 130 130" stroke="currentColor" stroke-width="4" fill="none" opacity="0.7" data-v-5e1c3535></path><path d="M80 140 Q100 120 120 140" stroke="currentColor" stroke-width="4" fill="none" data-v-5e1c3535></path><line x1="85" y1="85" x2="115" y2="115" stroke="#e74c3c" stroke-width="6" stroke-linecap="round" data-v-5e1c3535></line><line x1="115" y1="85" x2="85" y2="115" stroke="#e74c3c" stroke-width="6" stroke-linecap="round" data-v-5e1c3535></line><rect x="90" y="145" width="20" height="15" rx="2" stroke="currentColor" stroke-width="2" fill="none" data-v-5e1c3535></rect><circle cx="100" cy="152" r="2" fill="currentColor" data-v-5e1c3535></circle></svg></div>',1)),n("div",Am,[w[8]||(w[8]=n("h1",{class:"error-title"},"网络连接失败",-1)),n("p",Mm,[w[1]||(w[1]=ee(" 无法连接到服务器，请检查您的网络连接。",-1)),w[2]||(w[2]=n("br",null,null,-1)),ee(" "+S(u()),1)]),n("div",Lm,[w[7]||(w[7]=n("h3",null,"网络状态检测：",-1)),n("div",Om,[w[3]||(w[3]=n("span",{class:"status-label"},"网络连接:",-1)),n("span",{class:pe(["status-value",l.online?"status-online":"status-offline"])},S(l.online?"已连接":"已断开"),3)]),n("div",Rm,[w[4]||(w[4]=n("span",{class:"status-label"},"连接类型:",-1)),n("span",Im,S(l.type),1)]),n("div",Um,[w[5]||(w[5]=n("span",{class:"status-label"},"服务器状态:",-1)),n("span",{class:pe(["status-value",c.reachable?"status-online":"status-offline"])},S(c.reachable?"可访问":"无法访问"),3)]),c.reachable?J("",!0):(b(),C("div",Vm,[w[6]||(w[6]=n("span",{class:"status-label"},"响应时间:",-1)),n("span",jm,S(c.responseTime)+"ms",1)]))]),w[9]||(w[9]=n("div",{class:"network-solutions"},[n("h3",null,"解决方案："),n("ul",null,[n("li",null,"检查网络连接是否正常"),n("li",null,"尝试刷新页面或重新加载"),n("li",null,"检查WiFi或移动数据连接"),n("li",null,"重启路由器或调制解调器"),n("li",null,"联系网络服务提供商"),n("li",null,"检查防火墙或代理设置")])],-1))]),n("div",Nm,[n("button",{onClick:d,disabled:r.value,class:"action-btn primary"},[r.value?(b(),C("svg",Fm,w[11]||(w[11]=[n("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))):(b(),C("svg",Bm,w[10]||(w[10]=[n("path",{d:"M1 12S5 4 12 4S23 12 23 12S19 20 12 20S1 12 1 12Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("circle",{cx:"12",cy:"12",r:"3",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),ee(" "+S(r.value?"检测中...":"检测网络"),1)],8,Dm),n("button",{onClick:f,disabled:i.value,class:"action-btn secondary"},[i.value?(b(),C("svg",Km,w[13]||(w[13]=[n("circle",{cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4",fill:"none",opacity:"0.3"},null,-1),n("path",{d:"M12 2A10 10 0 0 1 22 12",stroke:"currentColor","stroke-width":"4",fill:"none","stroke-linecap":"round"},null,-1)]))):(b(),C("svg",zm,w[12]||(w[12]=[n("path",{d:"M1 4V10H7M23 20V14H17",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1),n("path",{d:"M20.49 9A9 9 0 0 0 5.64 5.64L1 10M23 14L18.36 18.36A9 9 0 0 1 3.51 15",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"},null,-1)]))),ee(" "+S(i.value?"重试中...":"重试连接"),1)],8,Hm),ue(T,{to:"/",class:"action-btn tertiary"},{default:me(()=>w[14]||(w[14]=[n("svg",{class:"btn-icon",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg"},[n("path",{d:"M3 9L12 2L21 9V20C21 20.5304 20.7893 21.0391 20.4142 21.4142C20.0391 21.7893 19.5304 22 19 22H5C4.46957 22 3.96086 21.7893 3.58579 21.4142C3.21071 21.0391 3 20.5304 3 20V9Z",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"}),n("path",{d:"M9 22V12H15V22",stroke:"currentColor","stroke-width":"2","stroke-linecap":"round","stroke-linejoin":"round"})],-1),ee(" 返回首页 ",-1)])),_:1,__:[14]})]),w[16]||(w[16]=Le('<div class="diagnostic-info" data-v-5e1c3535><h3 data-v-5e1c3535>网络诊断建议：</h3><div class="diagnostic-steps" data-v-5e1c3535><div class="diagnostic-step" data-v-5e1c3535><span class="step-number" data-v-5e1c3535>1</span><div class="step-content" data-v-5e1c3535><h4 data-v-5e1c3535>检查基础连接</h4><p data-v-5e1c3535>确认设备已连接到WiFi或移动网络，信号强度良好</p></div></div><div class="diagnostic-step" data-v-5e1c3535><span class="step-number" data-v-5e1c3535>2</span><div class="step-content" data-v-5e1c3535><h4 data-v-5e1c3535>测试其他网站</h4><p data-v-5e1c3535>尝试访问其他网站，确认是否为全局网络问题</p></div></div><div class="diagnostic-step" data-v-5e1c3535><span class="step-number" data-v-5e1c3535>3</span><div class="step-content" data-v-5e1c3535><h4 data-v-5e1c3535>重启网络设备</h4><p data-v-5e1c3535>重启路由器、调制解调器或移动设备的网络连接</p></div></div></div></div>',1))])])])}}}),Wm=Ue(Gm,[["__scopeId","data-v-5e1c3535"]]),pt=Vu({history:fu("/"),routes:[{path:"/",name:"home",component:Md,beforeEnter:jr,meta:{title:"膳食营养分析平台 - 智能营养管理"}},{path:"/login",name:"login",component:bf,beforeEnter:cn,meta:{title:"登录 - 膳食营养分析平台"}},{path:"/register",name:"register",component:c1,beforeEnter:cn,meta:{title:"注册 - 膳食营养分析平台"}},{path:"/forgot-password",name:"forgot-password",component:H1,beforeEnter:cn,meta:{title:"忘记密码 - 膳食营养分析平台"}},{path:"/dashboard",name:"dashboard",component:Sp,beforeEnter:(e,t,s)=>{jr(e,t,o=>{o?s(o):ts(["USER"])(e,t,s)})},meta:{title:"控制台 - 膳食营养分析平台"}},{path:"/admin/login",name:"admin-login",component:hh,beforeEnter:cn,meta:{title:"管理员登录 - 膳食营养分析平台"}},{path:"/admin/dashboard",name:"admin-dashboard",component:k0,beforeEnter:Ld,meta:{title:"管理后台 - 膳食营养分析平台"}},{path:"/nutrition",name:"nutrition",component:A2,beforeEnter:ts(["USER"]),meta:{title:"营养分析 - 膳食营养分析平台"}},{path:"/meals",name:"meals",component:O2,beforeEnter:ts(["USER"]),meta:{title:"膳食记录 - 膳食营养分析平台"}},{path:"/goals",name:"goals",component:U2,beforeEnter:ts(["USER"]),meta:{title:"健康目标 - 膳食营养分析平台"}},{path:"/reports",name:"reports",component:N2,beforeEnter:ts(["USER"]),meta:{title:"健康报告 - 膳食营养分析平台"}},{path:"/profile",name:"profile",component:Og,beforeEnter:ts(["USER"]),meta:{title:"个人资料 - 膳食营养分析平台"}},{path:"/settings",name:"settings",component:Ug,beforeEnter:ts(["USER"]),meta:{title:"设置 - 膳食营养分析平台"}},{path:"/error/403",name:"error-403",component:am,meta:{title:"访问被拒绝 - 膳食营养分析平台"}},{path:"/error/500",name:"error-500",component:Sm,meta:{title:"服务器错误 - 膳食营养分析平台"}},{path:"/error/network",name:"error-network",component:Wm,meta:{title:"网络错误 - 膳食营养分析平台"}},{path:"/:pathMatch(.*)*",name:"error-404",component:Gg,meta:{title:"页面未找到 - 膳食营养分析平台"}}]});pt.beforeEach(e=>{e.meta.title&&(document.title=e.meta.title)});class gn{static handleHttpError(t,s){const o={type:"UNKNOWN",message:"发生未知错误",timestamp:new Date};if(s)switch(o.code=s.status,s.status){case 403:o.type="PERMISSION",o.message="访问被拒绝，权限不足",pt.push("/error/403");return;case 404:o.type="NOT_FOUND",o.message="请求的资源不存在",pt.push("/error/404");return;case 500:case 502:case 503:case 504:o.type="SERVER",o.message="服务器内部错误",pt.push("/error/500");return;default:if(s.status>=400&&s.status<500)o.type="PERMISSION",o.message="客户端请求错误";else if(s.status>=500){o.type="SERVER",o.message="服务器错误",pt.push("/error/500");return}}else if(t){if(t.name==="TypeError"&&t.message.includes("fetch")){o.type="NETWORK",o.message="网络连接失败",pt.push("/error/network");return}else if(t.name==="AbortError"){o.type="NETWORK",o.message="请求超时",pt.push("/error/network");return}}this.logError(o)}static handleNetworkError(t){const s={type:"NETWORK",message:"网络连接失败",details:t,timestamp:new Date};this.logError(s),pt.push("/error/network")}static handlePermissionError(t){const s={type:"PERMISSION",code:403,message:t||"访问被拒绝",timestamp:new Date};this.logError(s),pt.push("/error/403")}static handleServerError(t,s){const o={type:"SERVER",code:t||500,message:s||"服务器内部错误",timestamp:new Date};this.logError(o),pt.push("/error/500")}static logError(t){console.error("[GlobalErrorHandler]",t),this.reportError(t)}static reportError(t){try{const s={type:t.type,code:t.code,message:t.message,details:t.details,timestamp:t.timestamp.toISOString(),userAgent:navigator.userAgent,url:window.location.href,userId:this.getCurrentUserId()};fetch("/api/error-report",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)}).catch(()=>{})}catch{}}static getCurrentUserId(){try{const t=localStorage.getItem("user_info");if(t)return JSON.parse(t).id||null}catch{}return null}}function qm(){window.addEventListener("unhandledrejection",e=>{if(console.error("Unhandled promise rejection:",e.reason),e.reason&&typeof e.reason=="object"&&e.reason.name==="TypeError"&&e.reason.message.includes("fetch")){gn.handleNetworkError(e.reason),e.preventDefault();return}gn.handleHttpError(e.reason),e.preventDefault()}),window.addEventListener("error",e=>{console.error("Global error:",e.error);const t={type:"UNKNOWN",message:e.message||"发生未知错误",details:{filename:e.filename,lineno:e.lineno,colno:e.colno,error:e.error},timestamp:new Date};gn.logError(t)})}function Zm(){window.addEventListener("online",()=>{console.log("Network connection restored")}),window.addEventListener("offline",()=>{console.log("Network connection lost"),gn.handleNetworkError(new Error("Network connection lost"))})}const jo=Tc(Ic);qm();Zm();Ce.initializeAuth();jo.use(Mc());jo.use(pt);jo.mount("#app");
